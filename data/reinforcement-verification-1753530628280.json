{"timestamp": "2025-07-26T11:50:23.226Z", "verifications": {"1m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "ensembleForest", "timeframe": "1m"}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "neuralNetwork", "timeframe": "1m"}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "adaptiveModel", "timeframe": "1m"}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "marketRegimeModel", "timeframe": "1m"}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "improvedEnsemble", "timeframe": "1m"}}, "5m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "ensembleForest", "timeframe": "5m"}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "neuralNetwork", "timeframe": "5m"}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "adaptiveModel", "timeframe": "5m"}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "marketRegimeModel", "timeframe": "5m"}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "improvedEnsemble", "timeframe": "5m"}}, "15m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "ensembleForest", "timeframe": "15m"}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "neuralNetwork", "timeframe": "15m"}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "adaptiveModel", "timeframe": "15m"}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "marketRegimeModel", "timeframe": "15m"}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "improvedEnsemble", "timeframe": "15m"}}, "1h": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "ensembleForest", "timeframe": "1h"}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "neuralNetwork", "timeframe": "1h"}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "adaptiveModel", "timeframe": "1h"}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "marketRegimeModel", "timeframe": "1h"}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "improvedEnsemble", "timeframe": "1h"}}, "4h": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "ensembleForest", "timeframe": "4h"}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "neuralNetwork", "timeframe": "4h"}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "adaptiveModel", "timeframe": "4h"}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "marketRegimeModel", "timeframe": "4h"}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0, "model": "improvedEnsemble", "timeframe": "4h"}}}, "reinforcementUpdates": {"1m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 1.1148387096774195, "newWeight": 1.1148387096774195, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5, "newWeight": 0.5, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 1.0451612903225806, "newWeight": 1.0451612903225806, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 1.0451612903225806, "newWeight": 1.0451612903225806, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 1.1148387096774195, "newWeight": 1.1148387096774195, "accuracy": 0, "improvement": 0}}, "5m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 1.0033548387096776, "newWeight": 1.0033548387096776, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5, "newWeight": 0.5, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.9406451612903226, "newWeight": 0.9406451612903226, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.9406451612903226, "newWeight": 0.9406451612903226, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 1.0033548387096776, "newWeight": 1.0033548387096776, "accuracy": 0, "improvement": 0}}, "15m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.9030193548387099, "newWeight": 0.9030193548387099, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5, "newWeight": 0.5, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.8465806451612904, "newWeight": 0.8465806451612904, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.8465806451612904, "newWeight": 0.8465806451612904, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.9030193548387099, "newWeight": 0.9030193548387099, "accuracy": 0, "improvement": 0}}, "1h": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.8127174193548389, "newWeight": 0.8127174193548389, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5, "newWeight": 0.5, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.7619225806451614, "newWeight": 0.7619225806451614, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.7619225806451614, "newWeight": 0.7619225806451614, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.8127174193548389, "newWeight": 0.8127174193548389, "accuracy": 0, "improvement": 0}}, "4h": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.731445677419355, "newWeight": 0.731445677419355, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5, "newWeight": 0.5, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.6857303225806453, "newWeight": 0.6857303225806453, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.6857303225806453, "newWeight": 0.6857303225806453, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.731445677419355, "newWeight": 0.731445677419355, "accuracy": 0, "improvement": 0}}}, "performanceImprovements": {}}