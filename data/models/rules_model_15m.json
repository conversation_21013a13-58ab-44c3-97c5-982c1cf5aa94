{"total": 995, "byTrend": {"UP": 136, "DOWN": 94, "LATERAL": 765}, "byRsiRange": {"neutral": {"UP": 102, "DOWN": 62, "LATERAL": 623}, "overbought": {"UP": 0, "DOWN": 1, "LATERAL": 3}, "high": {"UP": 34, "DOWN": 31, "LATERAL": 139}}, "byEmaTrend": {"NEUTRAL": {"UP": 83, "DOWN": 46, "LATERAL": 539}, "UP": {"UP": 42, "DOWN": 39, "LATERAL": 136}, "DOWN": {"UP": 11, "DOWN": 9, "LATERAL": 90}}, "byBollingerPosition": {"middle": {"UP": 46, "DOWN": 17, "LATERAL": 231}, "high": {"UP": 56, "DOWN": 50, "LATERAL": 402}, "extreme_high": {"UP": 34, "DOWN": 27, "LATERAL": 131}, "low": {"UP": 0, "DOWN": 0, "LATERAL": 1}}, "byMacdHistogram": {"neutral": {"UP": 13, "DOWN": 1, "LATERAL": 15}, "strong_negative": {"UP": 68, "DOWN": 51, "LATERAL": 405}, "strong_positive": {"UP": 54, "DOWN": 42, "LATERAL": 335}, "negative": {"UP": 1, "DOWN": 0, "LATERAL": 7}, "positive": {"UP": 0, "DOWN": 0, "LATERAL": 3}}, "rules": [{"condition": {"type": "rsi", "range": "neutral"}, "prediction": "LATERAL", "confidence": "0.79"}, {"condition": {"type": "rsi", "range": "high"}, "prediction": "LATERAL", "confidence": "0.68"}, {"condition": {"type": "emaTrend", "trend": "NEUTRAL"}, "prediction": "LATERAL", "confidence": "0.81"}, {"condition": {"type": "emaTrend", "trend": "UP"}, "prediction": "LATERAL", "confidence": "0.63"}, {"condition": {"type": "emaTrend", "trend": "DOWN"}, "prediction": "LATERAL", "confidence": "0.82"}, {"condition": {"type": "bollingerPosition", "range": "middle"}, "prediction": "LATERAL", "confidence": "0.79"}, {"condition": {"type": "bollingerPosition", "range": "high"}, "prediction": "LATERAL", "confidence": "0.79"}, {"condition": {"type": "bollingerPosition", "range": "extreme_high"}, "prediction": "LATERAL", "confidence": "0.68"}, {"condition": {"type": "macdHistogram", "range": "strong_negative"}, "prediction": "LATERAL", "confidence": "0.77"}, {"condition": {"type": "macdHistogram", "range": "strong_positive"}, "prediction": "LATERAL", "confidence": "0.78"}, {"condition": {"type": "combined", "conditions": [{"type": "rsi", "range": "oversold"}, {"type": "macdHistogram", "range": "positive"}]}, "prediction": "UP", "confidence": "0.75"}, {"condition": {"type": "combined", "conditions": [{"type": "rsi", "range": "overbought"}, {"type": "macdHistogram", "range": "negative"}]}, "prediction": "DOWN", "confidence": "0.75"}], "accuracy": "76.88", "timeframe": "15m", "createdAt": "2025-07-12T22:15:19.325Z"}