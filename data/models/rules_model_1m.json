{"total": 995, "byTrend": {"UP": 38, "DOWN": 51, "LATERAL": 906}, "byRsiRange": {"neutral": {"UP": 35, "DOWN": 47, "LATERAL": 822}, "high": {"UP": 3, "DOWN": 4, "LATERAL": 84}}, "byEmaTrend": {"NEUTRAL": {"UP": 38, "DOWN": 51, "LATERAL": 906}}, "byBollingerPosition": {"middle": {"UP": 18, "DOWN": 18, "LATERAL": 313}, "extreme_high": {"UP": 7, "DOWN": 9, "LATERAL": 196}, "high": {"UP": 12, "DOWN": 23, "LATERAL": 394}, "low": {"UP": 1, "DOWN": 1, "LATERAL": 3}}, "byMacdHistogram": {"neutral": {"UP": 0, "DOWN": 0, "LATERAL": 29}, "strong_positive": {"UP": 17, "DOWN": 20, "LATERAL": 413}, "strong_negative": {"UP": 19, "DOWN": 29, "LATERAL": 430}, "positive": {"UP": 0, "DOWN": 1, "LATERAL": 18}, "negative": {"UP": 2, "DOWN": 1, "LATERAL": 16}}, "rules": [{"condition": {"type": "rsi", "range": "neutral"}, "prediction": "LATERAL", "confidence": "0.91"}, {"condition": {"type": "rsi", "range": "high"}, "prediction": "LATERAL", "confidence": "0.92"}, {"condition": {"type": "emaTrend", "trend": "NEUTRAL"}, "prediction": "LATERAL", "confidence": "0.91"}, {"condition": {"type": "bollingerPosition", "range": "middle"}, "prediction": "LATERAL", "confidence": "0.90"}, {"condition": {"type": "bollingerPosition", "range": "extreme_high"}, "prediction": "LATERAL", "confidence": "0.92"}, {"condition": {"type": "bollingerPosition", "range": "high"}, "prediction": "LATERAL", "confidence": "0.92"}, {"condition": {"type": "macdHistogram", "range": "neutral"}, "prediction": "LATERAL", "confidence": "1.00"}, {"condition": {"type": "macdHistogram", "range": "strong_positive"}, "prediction": "LATERAL", "confidence": "0.92"}, {"condition": {"type": "macdHistogram", "range": "strong_negative"}, "prediction": "LATERAL", "confidence": "0.90"}, {"condition": {"type": "macdHistogram", "range": "positive"}, "prediction": "LATERAL", "confidence": "0.95"}, {"condition": {"type": "macdHistogram", "range": "negative"}, "prediction": "LATERAL", "confidence": "0.84"}, {"condition": {"type": "combined", "conditions": [{"type": "rsi", "range": "oversold"}, {"type": "macdHistogram", "range": "positive"}]}, "prediction": "UP", "confidence": "0.75"}, {"condition": {"type": "combined", "conditions": [{"type": "rsi", "range": "overbought"}, {"type": "macdHistogram", "range": "negative"}]}, "prediction": "DOWN", "confidence": "0.75"}], "accuracy": "91.06", "timeframe": "1m", "createdAt": "2025-07-12T22:15:19.315Z"}