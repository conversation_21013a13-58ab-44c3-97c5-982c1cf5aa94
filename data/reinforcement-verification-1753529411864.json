{"timestamp": "2025-07-26T11:30:06.809Z", "verifications": {"1m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0}}, "5m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0}}, "15m": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0}}, "1h": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0}}, "4h": {"ensembleForest": {"accuracy": 0, "predictions": 0, "correct": 0}, "neuralNetwork": {"accuracy": 0, "predictions": 0, "correct": 0}, "adaptiveModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "marketRegimeModel": {"accuracy": 0, "predictions": 0, "correct": 0}, "improvedEnsemble": {"accuracy": 0, "predictions": 0, "correct": 0}}}, "reinforcementUpdates": {"1m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.9, "newWeight": 0.9, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.9, "newWeight": 0.9, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.9, "newWeight": 0.9, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.9, "newWeight": 0.9, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.9, "newWeight": 0.9, "accuracy": 0, "improvement": 0}}, "5m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.81, "newWeight": 0.81, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.81, "newWeight": 0.81, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.81, "newWeight": 0.81, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.81, "newWeight": 0.81, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.81, "newWeight": 0.81, "accuracy": 0, "improvement": 0}}, "15m": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.7290000000000001, "newWeight": 0.7290000000000001, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.7290000000000001, "newWeight": 0.7290000000000001, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.7290000000000001, "newWeight": 0.7290000000000001, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.7290000000000001, "newWeight": 0.7290000000000001, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.7290000000000001, "newWeight": 0.7290000000000001, "accuracy": 0, "improvement": 0}}, "1h": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.6561000000000001, "newWeight": 0.6561000000000001, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.6561000000000001, "newWeight": 0.6561000000000001, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.6561000000000001, "newWeight": 0.6561000000000001, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.6561000000000001, "newWeight": 0.6561000000000001, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.6561000000000001, "newWeight": 0.6561000000000001, "accuracy": 0, "improvement": 0}}, "4h": {"ensembleForest": {"model": "ensembleForest", "previousWeight": 0.5904900000000002, "newWeight": 0.5904900000000002, "accuracy": 0, "improvement": 0}, "neuralNetwork": {"model": "neuralNetwork", "previousWeight": 0.5904900000000002, "newWeight": 0.5904900000000002, "accuracy": 0, "improvement": 0}, "adaptiveModel": {"model": "adaptiveModel", "previousWeight": 0.5904900000000002, "newWeight": 0.5904900000000002, "accuracy": 0, "improvement": 0}, "marketRegimeModel": {"model": "marketRegimeModel", "previousWeight": 0.5904900000000002, "newWeight": 0.5904900000000002, "accuracy": 0, "improvement": 0}, "improvedEnsemble": {"model": "improvedEnsemble", "previousWeight": 0.5904900000000002, "newWeight": 0.5904900000000002, "accuracy": 0, "improvement": 0}}}, "performanceImprovements": {}}