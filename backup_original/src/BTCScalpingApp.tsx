import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Activity, AlertCircle, BarChart3, Brain, Refresh<PERSON>w, FileText } from 'lucide-react';
import { calculateIndicators, Indicators, CandleWithVolume, Metrics, Prediction, Trend, Timeframe, PredictionHistoryEntry } from './utils/indicators';
import { AIModel } from './components/ModelSelector';
import TradingViewChart from './components/TradingViewChart';
import IndicatorsPanel from './components/IndicatorsPanel';
import MetricsPanel from './components/MetricsPanel';
import PredictionPanel from './components/PredictionPanel';
import PromptLogPanel from './components/PromptLogPanel';

declare global {
  interface Window {
    __ws_open_called?: boolean;
  }
}

type WebSocketStatus = 'CONNECTING' | 'CONNECTED' | 'DISCONNECTED' | 'RECONNECTING';

export function BTCScalpingApp() {
  const [candles, setCandles] = useState<CandleWithVolume[]>([]);
  const [indicators, setIndicators] = useState<Indicators | null>(null);
  const [prediction, setPrediction] = useState<Prediction | null>(null);
  const [metrics, setMetrics] = useState<Metrics | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastPrompt, setLastPrompt] = useState<string | null>(null);
  const [timeframeValutazione, setTimeframeValutazione] = useState<Timeframe>('1h');
  const [timeframeGrafico, setTimeframeGrafico] = useState<Timeframe>('1h');
  const [predictionHistory, setPredictionHistory] = useState<PredictionHistoryEntry[]>([]);
  const [wsStatus, setWsStatus] = useState<WebSocketStatus>('CONNECTING');
  const [wsError, setWsError] = useState<string | null>(null);
  const [wsReconnectAttempts, setWsReconnectAttempts] = useState(0);
  const [wsLog, setWsLog] = useState<string[]>([]);
  const [selectedModel, setSelectedModel] = useState<AIModel>('deepseek');
  const [lastUsedModel, setLastUsedModel] = useState<string | null>(null);

  const pushWsLog = (msg: string) => {
    setWsLog(prev => [
      `[${new Date().toLocaleTimeString()}] ${msg}`,
      ...prev.slice(0, 4)
    ]);
  };
  
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectAttemptsRef = useRef(0);

  // Funzione per effettuare predizioni
  const makePrediction = useCallback(async (currentIndicators: Indicators, price: number) => {
    const now = Date.now();
    try {
      const { rsi, macd, emaTrend, bbPosition } = currentIndicators;
      const payload = { 
        rsi, 
        macdHist: macd.histogram, 
        emaTrend, 
        bbPosition, 
        price, 
        timestamp: now, 
        modelType: selectedModel // Aggiungiamo il modello selezionato
      };
      const response = await fetch('http://localhost:3004/predict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Errore API: ${response.status}`);
      }
      const data = await response.json();
      setPrediction({ trend: data.prediction, timestamp: now, fallback: false });
      if (data.prompt) setLastPrompt(data.prompt);
      if (data.modelUsed) setLastUsedModel(data.modelUsed);
      setMetrics(prev => ({ ...prev, ...data.metrics }));
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Errore API di predizione';
      setError(`${message}. Utilizzo un valore di fallback.`);
      setPrediction({ trend: Math.random() > 0.5 ? 'UP' : 'DOWN', timestamp: now, fallback: true });
    }
  }, []);

  // Funzione per valutare le predizioni
  const valutaPredizioni = useCallback(async (timeframe: Timeframe) => {
    try {
      setError(null);
      // Valuta le predizioni con il timeframe specificato
      const responseEvaluate = await fetch('http://localhost:3004/evaluate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ timeframe }),
      });
      const dataEvaluate = await responseEvaluate.json();
      if (!responseEvaluate.ok) throw new Error(dataEvaluate.error || 'Errore sconosciuto dal server');
      
      // Log dei dati ricevuti per debug
      console.log('Dati ricevuti da /evaluate:', dataEvaluate);
      
      // Aggiorna le metriche con lo stesso timeframe
      await recuperaMetriche(timeframe);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Errore di rete';
      setError(`Valutazione fallita: ${message}`);
    }
  }, []);

  // Funzione per recuperare le metriche con il timeframe specificato
  const recuperaMetriche = useCallback(async (timeframe: Timeframe) => {
    try {
      setError(null);
      const response = await fetch(`http://localhost:3004/metrics?timeframe=${timeframe}`);
      const data = await response.json();
      if (!response.ok) throw new Error('Errore nel recupero delle metriche');
      
      console.log(`Metriche ricevute per timeframe ${timeframe}:`, data);
      
      if (data) {
        // Verifichiamo che i dati delle metriche siano numeri validi
        const validMetrics = {
          accuracy: typeof data.accuracy === 'number' ? data.accuracy : 0,
          precision: typeof data.precision === 'number' ? data.precision : 0,
          recall: typeof data.recall === 'number' ? data.recall : 0,
          f1Score: typeof data.f1Score === 'number' ? data.f1Score : 0,
          recentAccuracy: typeof data.recentAccuracy === 'number' ? data.recentAccuracy : 0,
          totalPredictions: typeof data.totalPredictions === 'number' ? data.totalPredictions : 0
        };
        console.log('Metriche validate:', validMetrics);
        setMetrics(validMetrics);
      }
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Errore di rete';
      setError(`Recupero metriche fallito: ${message}`);
    }
  }, []);
  
  // Gestione cambio timeframe per le valutazioni
  const gestisciCambioTimeframe = useCallback((nuovoTimeframe: Timeframe) => {
    setTimeframeValutazione(nuovoTimeframe);
    valutaPredizioni(nuovoTimeframe);
    recuperaMetriche(nuovoTimeframe);
  }, [valutaPredizioni, recuperaMetriche]);

  // Gestione cambio timeframe per il grafico
  const gestisciCambioTimeframeGrafico = useCallback((nuovoTimeframe: Timeframe) => {
    setTimeframeGrafico(nuovoTimeframe);
  }, []);

  // Gestione reset metriche
  const gestisciResetMetriche = useCallback(async () => {
    if (!window.confirm('Sei sicuro di voler resettare le metriche?')) return;
    try {
      const response = await fetch('http://localhost:3004/reset', { method: 'POST' });
      if (!response.ok) throw new Error('La richiesta di reset è fallita.');
      const data = await response.json();
      
      // Dopo il reset, recupera le metriche con il timeframe corrente
      await recuperaMetriche(timeframeValutazione);
      
      setPrediction(null);
      setLastPrompt(null);
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Errore sconosciuto';
      setError(`Impossibile resettare le metriche: ${message}`);
    }
  }, [timeframeValutazione, recuperaMetriche]);

  // Caricamento dello storico predizioni
  useEffect(() => {
    const recuperaStoricoPredizioni = async () => {
      try {
        const response = await fetch('http://localhost:3004/predictions');
        if (!response.ok) throw new Error('Impossibile recuperare lo storico delle predizioni');
        setPredictionHistory(await response.json());
      } catch (err) {
        setError('Impossibile caricare lo storico delle predizioni.');
      }
    };
    recuperaStoricoPredizioni();
  }, [metrics]);

  // Carica le metriche all'avvio dell'applicazione
  useEffect(() => {
    recuperaMetriche(timeframeValutazione);
  }, [recuperaMetriche, timeframeValutazione]);

  // Valutazione periodica delle predizioni
  useEffect(() => {
    valutaPredizioni(timeframeValutazione);
    const intervalId = setInterval(() => valutaPredizioni(timeframeValutazione), 60000);
    return () => clearInterval(intervalId);
  }, [valutaPredizioni, timeframeValutazione]);

  // Inizializza WebSocket e gestisce riconnessioni
  useEffect(() => {
    let isMounted = true;
    let reconnectTimeout: NodeJS.Timeout;
    let ws: WebSocket | null = null; // Dichiaro la variabile ws nello scope dell'useEffect
    
    if (window.__ws_open_called) return;
    window.__ws_open_called = true;

    const connectWebSocket = () => {
      pushWsLog('Connessione WebSocket in corso...');
      setWsStatus(reconnectAttemptsRef.current > 0 ? 'RECONNECTING' : 'CONNECTING');
      setWsReconnectAttempts(reconnectAttemptsRef.current);

      // Chiudi eventuali connessioni esistenti
      if (wsRef.current && (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING)) {
        try {
          wsRef.current.close();
        } catch (e) {
          console.error('Errore nella chiusura del WebSocket precedente:', e);
        }
      }

      // Utilizzo dello stream di candele da 1h di Binance
      ws = new WebSocket('wss://stream.binance.com:9443/ws/btcusdt@kline_1h');
      ws.binaryType = 'arraybuffer'; // Per gestire ping/pong binari
      wsRef.current = ws;

      ws.onopen = () => {
        if (!isMounted) return;
        setWsStatus('CONNECTED');
        reconnectAttemptsRef.current = 0;
        setWsReconnectAttempts(0);
        pushWsLog('WebSocket OPEN (connessione stabilita)');
        console.info('[WS] OPEN');
        window.__ws_open_called = true;
      };

      ws.onmessage = (event) => {
        // Gestione ping/pong binari
        if (typeof event.data !== 'string') {
          try {
            const data = new Uint8Array(event.data);
            if (data[0] === 0x9 && ws) { // 0x9 = opcode ping, controllo che ws non sia null
              ws.send(event.data); // Rispondi con pong (stesso payload)
              pushWsLog('Ricevuto PING, inviato PONG');
              console.info('[WS] PING → PONG');
              return;
            }
          } catch (e) {}
        }
        
        // Messaggio JSON normale
        let message: any;
        try {
          message = JSON.parse(event.data);
        } catch (e) {
          pushWsLog('Messaggio non JSON ignorato');
          return;
        }
        
        if (!message.k) return;
        pushWsLog('Ricevuto messaggio kline');
        
        const kline = message.k;
        // Convertire timestamp in secondi e assicurarsi che sia un intero
        // per evitare problemi di ordinamento con valori decimali
        const timestamp = Math.floor(kline.t / 1000);
        
        const newCandle: CandleWithVolume = {
          time: timestamp,
          open: parseFloat(kline.o), 
          high: parseFloat(kline.h),
          low: parseFloat(kline.l), 
          close: parseFloat(kline.c), 
          volume: parseFloat(kline.v),
        };
        
        console.log(`Nuova candela ricevuta: timestamp=${timestamp}, close=${newCandle.close}`);
        
        setCandles(prev => {
          // Usa una struttura Map per eliminare duplicati e garantire unicità per timestamp
          const candleMap = new Map(prev.map(c => [c.time, c]));
          candleMap.set(newCandle.time, newCandle);
          
          // Ordina esplicitamente per timestamp come numeri interi per evitare errori di ordinamento
          const updatedCandles = Array.from(candleMap.values())
            .sort((a, b) => Math.floor(Number(a.time)) - Math.floor(Number(b.time)));
          
          // Verifica che i dati siano effettivamente ordinati in modo ascendente
          for (let i = 1; i < updatedCandles.length; i++) {
            if (updatedCandles[i].time <= updatedCandles[i-1].time) {
              console.error(`Errore di ordinamento rilevato: ${updatedCandles[i-1].time} seguito da ${updatedCandles[i].time}`);
              // Correggi l'ordinamento aggiungendo un incremento minimo
              updatedCandles[i].time = updatedCandles[i-1].time + 1;
            }
          }
          
          // Se la candela è chiusa (kline.x === true), calcoliamo indicatori e facciamo predizione
          if (kline.x) {
            const newIndicators = calculateIndicators(updatedCandles);
            if (newIndicators) {
              setIndicators(newIndicators);
              makePrediction(newIndicators, newCandle.close);
            }
          }
          
          // Manteniamo solo le ultime 1000 candele per performance
          return updatedCandles.slice(-1000);
        });
      };

      ws.onerror = (event) => {
        if (!isMounted) return;
        setWsError('Errore WebSocket.');
        setWsStatus('DISCONNECTED');
        pushWsLog('WebSocket ERROR');
        console.error('[WS] ERROR', event);
      };

      ws.onclose = (event) => {
        if (!isMounted) return;
        setWsStatus('DISCONNECTED');
        pushWsLog(`WebSocket CLOSE (codice=${event.code}, motivo=${event.reason})`);
        console.warn('[WS] CLOSE', event);
        if (event.code !== 1000) {
          reconnectAttemptsRef.current++;
          reconnectTimeout = setTimeout(connectWebSocket, 3000);
        }
      };
    };

    // Caricamento dati storici prima di connettere il WebSocket
    const fetchInitialData = async () => {
      try {
        const response = await fetch('https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=100');
        const data = await response.json();
        const candleMap = new Map<number, CandleWithVolume>();
        data.forEach((k: any) => {
          const time = Math.floor(parseFloat(k[0]) / 1000);
          if (isNaN(time)) return;
          const candle: CandleWithVolume = {
            time: time, 
            open: parseFloat(k[1]), 
            high: parseFloat(k[2]),
            low: parseFloat(k[3]), 
            close: parseFloat(k[4]), 
            volume: parseFloat(k[5]),
          };
          if (!Object.values(candle).some(isNaN)) candleMap.set(time, candle);
        });
        const historicalCandles = Array.from(candleMap.values())
          .sort((a, b) => Number(a.time) - Number(b.time));
        setCandles(historicalCandles);
        connectWebSocket();
      } catch (err) {
        setError('Impossibile caricare i dati storici.');
      }
    };

    fetchInitialData();

    return () => {
      isMounted = false;
      clearTimeout(reconnectTimeout);
      if (wsRef.current) wsRef.current.close(1000, 'Component unmounting');
    };
  }, []);

  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-wrap gap-2 mb-4 p-2 bg-gray-800 rounded-md">
        <div className="flex items-center">
          <span className={`h-3 w-3 rounded-full mr-2 ${
            wsStatus === 'CONNECTED' 
              ? 'bg-green-500' 
              : wsStatus === 'CONNECTING' 
                ? 'bg-yellow-500' 
                : wsStatus === 'RECONNECTING' 
                  ? 'bg-orange-500' 
                  : 'bg-red-500'
          }`} />
          <span className="text-sm">
            {wsStatus === 'CONNECTED' 
              ? 'WebSocket connesso' 
              : wsStatus === 'CONNECTING' 
                ? 'Connessione in corso...' 
                : wsStatus === 'RECONNECTING' 
                  ? `Riconnessione (${wsReconnectAttempts})...` 
                  : 'Disconnesso'}
          </span>
        </div>
        {wsLog.length > 0 && (
          <div className="flex items-center ml-4">
            <span className="text-xs opacity-70">{wsLog[0]}</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-12 gap-4">
        {/* Grafico principale */}
        <div className="col-span-12 lg:col-span-8">
          <div className="bg-gray-800 p-4 rounded-md mb-4">
              <h2 className="text-lg font-semibold mb-2 flex items-center">
                <BarChart3 size={18} className="mr-2" /> Grafico BTC/USDT ({timeframeGrafico})
              </h2>
            <div className="h-[600px] relative">
              {/* Nuovo componente TradingViewChart */}
              <TradingViewChart 
                candles={candles} 
                predictionHistory={predictionHistory} 
                evaluationTimeframe={timeframeValutazione}
                onTimeframeChange={gestisciCambioTimeframeGrafico}
                chartTimeframe={timeframeGrafico}
              />
            </div>
          </div>
        </div>

        <div className="col-span-12 lg:col-span-4">
          {/* Pannello indicatori */}
          <div className="bg-gray-800 p-4 rounded-md mb-4">
            <h2 className="text-lg font-semibold mb-4 flex items-center">
              <Activity size={18} className="mr-2" /> Indicatori
            </h2>
            <IndicatorsPanel indicators={indicators} />
          </div>

          {/* Pannello predizioni */}
          <div className="bg-gray-800 p-4 rounded-md mb-4">
            <h2 className="text-lg font-semibold mb-2 flex items-center">
              <Brain size={18} className="mr-2" /> Predizione
            </h2>
            <PredictionPanel 
              prediction={prediction} 
              selectedModel={selectedModel} 
              onModelChange={setSelectedModel}
              modelUsed={lastUsedModel}
            />
          </div>

          {/* Pannello metriche */}
          <div className="bg-gray-900 p-4 rounded-lg border border-gray-800 mb-4">
            <MetricsPanel 
              metrics={metrics} 
              selectedTimeframe={timeframeValutazione}
              onTimeframeChange={gestisciCambioTimeframe}
              onReset={gestisciResetMetriche}
            />
          </div>

          {/* Pannello prompt */}
          <div className="bg-gray-900 p-4 rounded-lg border border-gray-800 mb-4">
            <PromptLogPanel lastPrompt={lastPrompt} />
          </div>
        </div>
      </div>

      {/* Gestione errori */}
      {error && (
        <div className="fixed bottom-4 left-4 right-4 bg-red-600 text-white p-4 rounded-md flex items-center">
          <AlertCircle className="mr-2" size={20} />
          <span>{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto bg-red-700 px-2 py-1 rounded hover:bg-red-800"
          >
            Chiudi
          </button>
        </div>
      )}
    </div>
  );
}