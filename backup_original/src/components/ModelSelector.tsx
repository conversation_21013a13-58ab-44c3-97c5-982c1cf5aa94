import React from 'react';

export type AIModel = 'deepseek' | 'grok4' | 'gemini';

interface ModelSelectorProps {
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({ selectedModel, onModelChange }) => {
  const models: { value: AIModel; label: string; description: string }[] = [
    { value: 'deepseek', label: 'DeepSeek', description: 'Modello predefinito' },
    { value: 'grok4', label: 'Grok 4', description: 'Via OpenRouter' },
    { value: 'gemini', label: 'Gemini', description: 'Google AI' }
  ];

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center">
        <h3 className="text-md font-semibold mb-2">Modello AI</h3>
      </div>
      <div className="grid grid-cols-3 gap-2">
        {models.map((model) => (
          <button
            key={model.value}
            className={`p-2 rounded-md text-center transition-all ${
              selectedModel === model.value
                ? 'bg-blue-600 text-white'
                : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
            }`}
            onClick={() => onModelChange(model.value)}
          >
            <div className="font-medium">{model.label}</div>
            <div className="text-xs opacity-80 mt-1">{model.description}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModelSelector;
