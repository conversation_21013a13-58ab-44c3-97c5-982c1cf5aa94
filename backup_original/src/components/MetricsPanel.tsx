import React from 'react';
import { BarChart3, RefreshCw } from 'lucide-react';

import { Metrics, Timeframe } from '../utils/indicators';

type MetricsPanelProps = {
  metrics: Metrics | null;
  onReset: () => void;
  onTimeframeChange: (timeframe: Timeframe) => void;
  selectedTimeframe: Timeframe;
};

const timeframes: Timeframe[] = ['5m', '10m', '15m', '1h', '4h', '8h', '24h'];

const MetricsPanel: React.FC<MetricsPanelProps> = ({ metrics, onReset, onTimeframeChange, selectedTimeframe }) => {
  if (!metrics) return null;
  
  // Log per debug dei valori ricevuti
  console.log('Metriche ricevute:', metrics);

  return (
    <div className="p-4 rounded-lg bg-gray-900 border border-gray-800">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-xl font-bold text-white flex items-center"><BarChart3 className="mr-2 text-blue-400" /> Performance</h3>
        <div className="flex items-center space-x-2">
          <div className="flex items-center bg-gray-800 rounded-md p-0.5">
            {timeframes.map(tf => (
              <button
                key={tf}
                onClick={() => onTimeframeChange(tf)}
                className={`px-2 py-0.5 text-xs rounded-md transition-colors ${
                  selectedTimeframe === tf
                    ? 'bg-blue-500 text-white'
                    : 'bg-transparent text-gray-300 hover:bg-gray-700'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>
          <button 
            onClick={onReset}
            className="p-1 bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
            title="Reset Metrics"
          >
            <RefreshCw className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
        <div>
          <p className="text-gray-300 font-medium mb-1">Accuracy</p>
          <p className="text-xl font-bold text-white">
            {(typeof metrics.accuracy === 'number' ? metrics.accuracy : 0).toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">Precisione</p>
          <p className="text-xl font-bold text-white">
            {(typeof metrics.precision === 'number' ? metrics.precision : 0).toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">Recall</p>
          <p className="text-xl font-bold text-white">
            {(typeof metrics.recall === 'number' ? metrics.recall : 0).toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">F1-Score</p>
          <p className="text-xl font-bold text-white">
            {(typeof metrics.f1Score === 'number' ? metrics.f1Score : 0).toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">Recente (20)</p>
          <p className="text-xl font-bold text-white">
            {(typeof metrics.recentAccuracy === 'number' ? metrics.recentAccuracy : 0).toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">Totali</p>
          <p className="text-xl font-bold text-white">{metrics.totalPredictions || 0}</p>
        </div>
      </div>
    </div>
  );
};

export default MetricsPanel;
