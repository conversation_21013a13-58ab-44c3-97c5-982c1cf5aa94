import React, { useEffect, useRef, useMemo } from 'react';
import { createChart, CandlestickSeries, createSeriesMarkers } from 'lightweight-charts';
import { CandleWithVolume, PredictionHistoryEntry, Timeframe } from '../utils/indicators';
import { aggregateCandles } from '../utils/candleAggregator';

type ChartMarker = {
  time: number;
  position: 'aboveBar' | 'belowBar' | 'inBar';
  color: string;
  shape: 'circle' | 'square' | 'arrowUp' | 'arrowDown';
  size: number;
};

interface TradingViewChartProps {
  candles: CandleWithVolume[];
  predictionHistory: PredictionHistoryEntry[];
  evaluationTimeframe: Timeframe;
  onTimeframeChange?: (timeframe: Timeframe) => void;
  chartTimeframe?: Timeframe;
}

const timeframes: Timeframe[] = ['5m', '10m', '15m', '1h', '4h', '8h', '24h'];

// Mappatura della spaziatura delle barre per ciascun timeframe
const spaziaturaBarrePerTimeframe: Record<Timeframe, number> = {
  '5m': 8,   // Spaziatura minima per timeframe brevi
  '10m': 10,
  '15m': 12,
  '1h': 18,  // Spaziatura media per timeframe intermedi
  '4h': 22,
  '8h': 25,  // Spaziatura maggiore per timeframe lunghi
  '24h': 30
};

const TradingViewChart: React.FC<TradingViewChartProps> = ({
  candles,
  predictionHistory,
  evaluationTimeframe,
  onTimeframeChange,
  chartTimeframe = '1h',
}) => {
  // Riferimenti al grafico e alle serie
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<any>(null);
  const candleSeriesRef = useRef<any>(null);
  const markersRef = useRef<any>(null);

  // Memorizza la spaziatura corrente delle barre in base al timeframe
  const spaziaturaCorrente = useMemo(() => spaziaturaBarrePerTimeframe[chartTimeframe], [chartTimeframe]);

  // Inizializzazione del grafico
  useEffect(() => {
    if (!chartContainerRef.current || chartRef.current) return;

    // Creazione del grafico con colori ottimizzati per massima leggibilità
    const chart = createChart(chartContainerRef.current, {
      layout: {
        background: { color: '#131722' },
        textColor: '#FFFFFF', // Testo bianco brillante per massimo contrasto
        fontFamily: 'Roboto, Arial, sans-serif',
      },
      grid: {
        vertLines: { color: 'rgba(70, 70, 90, 0.2)', visible: true }, // Griglia ancora più discreta
        horzLines: { color: 'rgba(70, 70, 90, 0.2)', visible: true }, // Griglia ancora più discreta
      },
      crosshair: {
        mode: 1, // CrosshairMode.Normal
        vertLine: {
          width: 1,
          style: 2, // LineStyle.Dashed
          color: 'rgba(255, 255, 255, 0.3)', // Colore più visibile
          labelBackgroundColor: '#2962FF', // Blu brillante per le etichette
        },
        horzLine: {
          width: 1,
          style: 2, // LineStyle.Dashed
          color: 'rgba(255, 255, 255, 0.3)', // Colore più visibile
          labelBackgroundColor: '#2962FF', // Blu brillante per le etichette
        },
      },
      width: chartContainerRef.current.clientWidth,
      height: 600,
      timeScale: {
        timeVisible: true,
        secondsVisible: true,
        borderColor: '#485c7b',
        // Non possiamo usare textColor qui perché non è supportato dal tipo
        tickMarkFormatter: (time: number) => {
          const date = new Date(time * 1000);
          return date.getHours() + ':' + date.getMinutes().toString().padStart(2, '0');
        },
        barSpacing: spaziaturaCorrente, // Spazio tra le candele in base al timeframe selezionato
        rightOffset: 5, // Spazio a destra dell'ultima candela
        fixLeftEdge: true, // Impedisce lo scroll infinito a sinistra
      },
      rightPriceScale: {
        borderColor: '#444',
        textColor: '#FFFFFF', // Testo bianco brillante per i valori di prezzo
        scaleMargins: {
          top: 0.3,
          bottom: 0.25,
        },
        entireTextOnly: true, // Mostra i numeri interi senza tagliarli
      },
      localization: {
        locale: 'it-IT',
      },
    });

    // Aggiunta serie delle candele (sintassi v5)
    const candleSeries = chart.addSeries(CandlestickSeries, {
      upColor: '#4CAF50', // Verde più brillante per le candele up
      downColor: '#FF5252', // Rosso più brillante per le candele down
      borderVisible: false,
      wickUpColor: '#4CAF50', 
      wickDownColor: '#FF5252',
    });

    // Inizializziamo i markers (nuovo metodo in v5)
    markersRef.current = createSeriesMarkers(candleSeries, []);

    chartRef.current = chart;
    candleSeriesRef.current = candleSeries;

    // Limita la visualizzazione a circa 2-3 giorni di dati (48-72 candele da 1 ora)
    const limitVisibleRange = () => {
      if (candles.length > 0) {
        // Determina il range temporale da mostrare (massimo ~48 candele = 2 giorni)
        const maxCandlesToShow = 48;
        const visibleCandlesCount = Math.min(candles.length, maxCandlesToShow);

        // Se abbiamo più di 120 candele, mostriamo solo le più recenti
        if (candles.length > maxCandlesToShow) {
          const from = candles.length - visibleCandlesCount;
          const to = candles.length - 1;

          // Imposta il range logico visibile
          chart.timeScale().setVisibleLogicalRange({
            from,
            to,
          });
        } else {
          // Altrimenti mostra tutto e adatta al contenuto
          chart.timeScale().fitContent();
        }
      }
    };

    limitVisibleRange();

    // Gestione del ridimensionamento
    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        chartRef.current.resize(
          chartContainerRef.current.clientWidth,
          600
        );
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, []);

  // Aggiornamento dei dati delle candele
  useEffect(() => {
    if (!candleSeriesRef.current || candles.length === 0) return;

    // Aggregazione delle candele in base al timeframe selezionato
    const aggregatedCandles = aggregateCandles(candles, chartTimeframe);
    console.log(`Candele aggregate per timeframe ${chartTimeframe}:`, aggregatedCandles.length);

    // Elaborazione delle candele per il formato corretto
    const processedCandles = aggregatedCandles.map(candle => {
      let timeValue: number;

      // Ora che abbiamo ridefinito CandleWithVolume con time: number, gestiamo solo numeri
      timeValue = candle.time;

      // Assicuriamoci che il timestamp sia in secondi (se è in millisecondi)
      if (timeValue > 10000000000) {
        timeValue = Math.floor(timeValue / 1000);
      }

      return {
        time: timeValue,
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
      };
    });

    candleSeriesRef.current.setData(processedCandles);

    // Limita la visualizzazione a circa 2 giorni (48 candele da 1 ora)
    const maxCandlesToShow = 48;
    if (aggregatedCandles.length > maxCandlesToShow) {
      const from = aggregatedCandles.length - maxCandlesToShow;
      const to = aggregatedCandles.length - 1;

      chartRef.current?.timeScale().setVisibleRange({
        from: processedCandles[from].time,
        to: processedCandles[to].time,
      });
    } else {
      chartRef.current.timeScale().fitContent();
    }
  }, [candles, chartTimeframe]);

  // Aggiornamento dei marker per le predizioni
  useEffect(() => {
    if (!candleSeriesRef.current || predictionHistory.length === 0) return;
    
    // Creazione dei marker per le predizioni
    const markers: ChartMarker[] = predictionHistory
      // Mostra tutte le predizioni, indipendentemente dalla valutazione
      .map(p => {
        // Determinazione della forma in base al tipo di predizione
        let shape: 'circle' | 'square' | 'arrowUp' | 'arrowDown' = 'circle';
        
        if (p.prediction === 'UP') {
          shape = 'arrowUp';
        } else if (p.prediction === 'DOWN') {
          shape = 'arrowDown';
        } // Per LATERAL rimane circle
        
        // Determina il colore in base alla valutazione (se esiste)
        // Se non c'è valutazione per il timeframe corrente, usa un colore neutro
        let color = '#aaaaaa'; // Colore grigio neutro per predizioni senza valutazione
        
        // Se esiste una valutazione per questo timeframe, usa il colore appropriato
        if (p.evaluation?.[evaluationTimeframe]?.status) {
          color = p.evaluation[evaluationTimeframe].status === 'correct' ? '#26a69a' : '#ef5350';
        }
        
        return {
          time: Math.floor(p.timestamp / 1000),
          position: 'aboveBar',
          color: color,
          shape: shape,
          size: 2, // Dimensione aumentata per migliorare la visibilità
        };
      });
    
    // Imposta i marker utilizzando l'API markers (nuovo metodo in v5)
    if (markersRef.current && markers.length > 0) {
      markersRef.current.setMarkers(markers);
    }
  }, [predictionHistory, evaluationTimeframe]);

  // Effetto per aggiornare la spaziatura delle barre quando cambia il timeframe
  useEffect(() => {
    if (chartRef.current) {
      chartRef.current.applyOptions({
        timeScale: {
          barSpacing: spaziaturaCorrente,
        },
      });
      
      // Forza l'aggiornamento dei dati quando cambia il timeframe
      if (candleSeriesRef.current && candles.length > 0) {
        const aggregatedCandles = aggregateCandles(candles, chartTimeframe);
        const processedCandles = aggregatedCandles.map(candle => {
          let timeValue = candle.time;
          if (timeValue > 10000000000) {
            timeValue = Math.floor(timeValue / 1000);
          }
          return {
            time: timeValue,
            open: candle.open,
            high: candle.high,
            low: candle.low,
            close: candle.close,
          };
        });
        candleSeriesRef.current.setData(processedCandles);
      }
    }
  }, [chartTimeframe, spaziaturaCorrente, candles]);

  return (
    <div className="relative">
      {onTimeframeChange && (
        <div className="absolute top-2 right-2 z-10 bg-gray-800 rounded-md p-1 shadow-lg">
          <div className="flex items-center space-x-1">
            {timeframes.map(tf => (
              <button
                key={tf}
                onClick={() => onTimeframeChange(tf)}
                className={`px-2 py-0.5 text-xs rounded-md transition-colors ${
                  chartTimeframe === tf
                    ? 'bg-blue-500 text-white'
                    : 'bg-transparent text-gray-300 hover:bg-gray-700'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>
        </div>
      )}
      <div ref={chartContainerRef} style={{ width: '100%', height: '600px' }} />
    </div>
  );
};

export default TradingViewChart;
