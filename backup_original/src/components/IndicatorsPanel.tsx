import React from 'react';
import { Activity } from 'lucide-react';

import { Indicators } from '../utils/indicators';

type IndicatorsPanelProps = {
  indicators: Indicators | null;
};

const IndicatorsPanel: React.FC<IndicatorsPanelProps> = ({ indicators }) => {
  if (!indicators) return null;

  return (
    <div className="bg-gray-900 p-4 rounded-lg border border-gray-800">
      <h3 className="text-xl font-bold mb-3 flex items-center text-white"><Activity className="mr-2 text-blue-400"/> Indicatori Tecnici</h3>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <p className="text-gray-300 font-medium mb-1">RSI</p>
          <p className={`text-xl font-bold ${indicators.rsi < 30 ? 'text-green-500' : indicators.rsi > 70 ? 'text-red-500' : 'text-white'}`}>{indicators.rsi.toFixed(2)}</p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">MACD Hist</p>
          <p className={`text-xl font-bold ${indicators.macd.histogram > 0 ? 'text-green-500' : 'text-red-500'}`}>{indicators.macd.histogram.toFixed(4)}</p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">EMA Trend</p>
          <p className={`text-xl font-bold ${indicators.emaTrend === 'BULLISH' ? 'text-green-500' : indicators.emaTrend === 'BEARISH' ? 'text-red-500' : 'text-white'}`}>{indicators.emaTrend}</p>
        </div>
        <div>
          <p className="text-gray-300 font-medium mb-1">BB Position</p>
          <p className={`text-xl font-bold ${indicators.bbPosition < 0.2 ? 'text-green-500' : indicators.bbPosition > 0.8 ? 'text-red-500' : 'text-white'}`}>{indicators.bbPosition.toFixed(2)}</p>
        </div>
      </div>
    </div>
  );
};

export default IndicatorsPanel;
