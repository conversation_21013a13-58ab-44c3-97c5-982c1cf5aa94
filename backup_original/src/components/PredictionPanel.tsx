import React from 'react';
// Importo solo icone sicuramente presenti in lucide-react
import { ArrowUp, ArrowDown, Minus, Brain } from 'lucide-react';

import { Prediction } from '../utils/indicators';
import ModelSelector, { AIModel } from './ModelSelector';

type PredictionPanelProps = {
  prediction: Prediction | null;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  modelUsed?: string | null;
};

const PredictionPanel: React.FC<PredictionPanelProps> = ({ 
  prediction, 
  selectedModel, 
  onModelChange,
  modelUsed
}) => {
  return (
    <div className="space-y-4">
      <ModelSelector selectedModel={selectedModel} onModelChange={onModelChange} />
      
      {!prediction ? (
        <div className="p-4 rounded-lg bg-gray-900 border border-gray-800 flex items-center justify-center space-x-4">
          <p className="text-gray-300">In attesa della predizione...</p>
        </div>
      ) : (() => {
        // Mappa delle icone trend con fallback sicuro
        const icons = { UP: ArrowUp, DOWN: ArrowDown, LATERAL: Minus };
        const TrendIcon = icons[prediction.trend as keyof typeof icons] || Minus;
        const color = { UP: 'text-green-500', DOWN: 'text-red-500', LATERAL: 'text-gray-400' }[prediction.trend];

        return (
          <div className="p-4 rounded-lg bg-gray-900 border border-gray-800">
            <h3 className="text-xl font-bold mb-3 text-white flex items-center">
              <Brain className="mr-2 text-blue-400" /> Predizione AI
            </h3>
            <div className={`flex items-center justify-center space-x-4 ${prediction.fallback ? 'border border-yellow-500 bg-yellow-500 bg-opacity-10 p-3 rounded-lg' : 'p-2'}`}>
              <TrendIcon className={`${color} h-12 w-12`} />
              <div>
                <p className={`text-2xl font-bold ${color}`}>{prediction.trend}</p>
                <p className="text-sm text-gray-300 mt-1">@ {new Date(prediction.timestamp).toLocaleTimeString()}</p>
                {modelUsed && (
                  <p className="text-xs text-blue-300 mt-1">
                    Modello: {modelUsed === 'deepseek' ? 'DeepSeek' : 
                             modelUsed === 'grok4' ? 'Grok 4' : 
                             modelUsed === 'gemini' ? 'Gemini' : modelUsed}
                  </p>
                )}
                {prediction.fallback && (
                  <p className="text-sm text-yellow-500 font-medium mt-1">Fallback: Errore API</p>
                )}
              </div>
            </div>
          </div>
        );
      })()}
    </div>
  );
};

export default PredictionPanel;
