import React from 'react';
import { FileText } from 'lucide-react';

interface Props {
  lastPrompt: string | null;
}

const PromptLogPanel: React.FC<Props> = ({ lastPrompt }) => {
  return (
    <div className="bg-gray-900 p-4 rounded-lg shadow-lg border border-gray-800">
      <h2 className="text-xl font-bold mb-3 flex items-center text-white">
        <FileText className="mr-2 h-6 w-6 text-blue-400" />
        Ultimo Prompt
      </h2>
      {lastPrompt ? (
        <pre className="text-sm text-white bg-gray-800 p-3 rounded-lg overflow-x-auto whitespace-pre-wrap break-words border border-gray-700">
          {lastPrompt}
        </pre>
      ) : (
        <div className="text-center text-gray-300 p-3">Waiting for prompt...</div>
      )}
    </div>
  );
};

export default PromptLogPanel;
