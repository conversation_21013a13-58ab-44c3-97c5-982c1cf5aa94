// src/utils/indicators.ts
import { CandlestickData } from 'lightweight-charts';
import * as indicatorsUtil from './technicalIndicators';

// Ridefinizione di CandleWithVolume per utilizzare number invece di Time
export interface CandleWithVolume {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}

export interface Indicators {
  rsi: number;
  macd: { macd: number; signal: number; histogram: number; };
  emaTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
  bbPosition: number;
  volumeRatio: number;
}

export interface Metrics {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  recentAccuracy: number;
  totalPredictions: number;
}

export type Trend = 'UP' | 'DOWN' | 'LATERAL';

export type Timeframe = '5m' | '10m' | '15m' | '1h' | '4h' | '8h' | '24h';

export interface Prediction {
  trend: Trend;
  timestamp: number;
  fallback?: boolean;
}

// Nuovo tipo per lo storico delle predizioni che riceviamo dal backend
export interface PredictionHistoryEntry {
  id: number;
  timestamp: number;
  priceAtPrediction: number;
  prediction: Trend;
  indicators: Indicators;
  evaluation: {
    [key in Timeframe]?: {
      status: 'pending' | 'correct' | 'incorrect';
      actualTrend?: Trend;
      evaluatedAt?: number;
      priceAtEvaluation?: number;
    }
  };
}


export const calculateIndicators = (candles: CandleWithVolume[]): Indicators | null => {
  if (candles.length < 20) return null;

  const closePrices = candles.map(c => c.close);
  const volumes = candles.map(c => c.volume || 0);

  const rsi = indicatorsUtil.calculateRSI(closePrices, 7);
  const macdResult = indicatorsUtil.calculateMACD(closePrices);
  const ema5 = indicatorsUtil.calculateEMA(closePrices, 5).pop();
  const ema8 = indicatorsUtil.calculateEMA(closePrices, 8).pop();
  const ema13 = indicatorsUtil.calculateEMA(closePrices, 13).pop();
  const bb = indicatorsUtil.calculateBollingerBands(closePrices, 14, 2.5);

  if (ema5 === undefined || ema8 === undefined || ema13 === undefined) return null;

  let emaTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
  if (ema5 > ema8 && ema8 > ema13) emaTrend = 'BULLISH';
  if (ema5 < ema8 && ema8 < ema13) emaTrend = 'BEARISH';

  const bbPosition = bb.position;

  const avgVolumeSma = indicatorsUtil.calculateSMA(volumes, 20);
  const avgVolume = avgVolumeSma.pop() || 1;
  const lastVolume = volumes[volumes.length - 1];
  const volumeRatio = lastVolume / avgVolume;

  return {
    rsi,
    macd: { macd: macdResult.macdLine, signal: macdResult.signalLine, histogram: macdResult.histogram },
    emaTrend,
    bbPosition,
    volumeRatio,
  };
};
