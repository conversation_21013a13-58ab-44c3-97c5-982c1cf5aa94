// src/utils/technicalIndicators.ts

export interface Candle {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export const calculateSMA = (data: number[], period: number): number[] => {
  const sma: number[] = [];
  for (let i = period - 1; i < data.length; i++) {
    const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val, 0);
    sma.push(sum / period);
  }
  return sma;
};

export const calculateEMA = (data: number[], period: number): number[] => {
  const k = 2 / (period + 1);
  const ema: number[] = [];
  if (data.length > 0) {
    ema.push(data[0]);
    for (let i = 1; i < data.length; i++) {
      ema.push(data[i] * k + ema[i - 1] * (1 - k));
    }
  }
  return ema;
};

export const calculateRSI = (data: number[], period: number = 14): number => {
  if (data.length < period) return 50;
  let gains = 0;
  let losses = 0;

  for (let i = 1; i < data.length; i++) {
    const change = data[i] - data[i - 1];
    if (change > 0) {
      gains += change;
    } else {
      losses -= change;
    }
  }

  const avgGain = gains / period;
  const avgLoss = losses / period;

  if (avgLoss === 0) return 100;

  const rs = avgGain / avgLoss;
  return 100 - (100 / (1 + rs));
};

export const calculateMACD = (data: number[], shortPeriod: number = 12, longPeriod: number = 26, signalPeriod: number = 9) => {
  const emaShort = calculateEMA(data, shortPeriod);
  const emaLong = calculateEMA(data, longPeriod);
  const macdLine = emaShort.map((val, i) => val - emaLong[i]);
  const signalLine = calculateEMA(macdLine, signalPeriod);
  const histogram = macdLine.map((val, i) => val - signalLine[i]);

  return {
    macdLine: macdLine[macdLine.length - 1] || 0,
    signalLine: signalLine[signalLine.length - 1] || 0,
    histogram: histogram[histogram.length - 1] || 0,
  };
};

export const calculateBollingerBands = (data: number[], period: number = 20, stdDev: number = 2) => {
  const sma = calculateSMA(data, period);
  const lastSma = sma[sma.length - 1];
  const slice = data.slice(-period);
  const std = Math.sqrt(slice.map(val => Math.pow(val - lastSma, 2)).reduce((a, b) => a + b) / period);
  
  const upper = lastSma + std * stdDev;
  const lower = lastSma - std * stdDev;
  const lastPrice = data[data.length - 1];
  const position = (lastPrice - lower) / (upper - lower);

  return { upper, lower, middle: lastSma, position };
};
