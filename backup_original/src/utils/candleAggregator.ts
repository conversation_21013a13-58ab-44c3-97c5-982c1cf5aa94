// src/utils/candleAggregator.ts
import { CandleWithVolume, Timeframe } from './indicators';

// Funzione per convertire il timeframe in minuti
export const timeframeToMinutes = (timeframe: Timeframe): number => {
  switch (timeframe) {
    case '5m': return 5;
    case '10m': return 10;
    case '15m': return 15;
    case '1h': return 60;
    case '4h': return 240;
    case '8h': return 480;
    case '24h': return 1440;
    default: return 60; // Default a 1 ora
  }
};

// Funzione per determinare il timestamp di inizio del periodo
export const getIntervalStartTime = (timestamp: number, intervalMinutes: number): number => {
  // Converti timestamp in millisecondi se è in secondi
  const timestampMs = timestamp > 10000000000 ? timestamp : timestamp * 1000;
  const date = new Date(timestampMs);
  
  // Calcola l'inizio dell'intervallo
  const minutes = date.getUTCMinutes();
  const hours = date.getUTCHours();
  const totalMinutes = hours * 60 + minutes;
  
  // Arrotonda al periodo più vicino
  const intervalStart = Math.floor(totalMinutes / intervalMinutes) * intervalMinutes;
  const newHours = Math.floor(intervalStart / 60);
  const newMinutes = intervalStart % 60;
  
  // Crea una nuova data con l'inizio dell'intervallo
  const newDate = new Date(date);
  newDate.setUTCHours(newHours, newMinutes, 0, 0);
  
  // Restituisci il timestamp in millisecondi
  return newDate.getTime();
};

// Funzione per aggregare le candele in base al timeframe
export const aggregateCandles = (candles: CandleWithVolume[], timeframe: Timeframe): CandleWithVolume[] => {
  if (!candles.length) return [];
  if (timeframe === '5m' && candles[0].time > 10000000000) {
    // Se il timeframe è 5m e le candele sono già in millisecondi, assumiamo che siano già candele da 5 minuti
    return candles;
  }
  
  const intervalMinutes = timeframeToMinutes(timeframe);
  const groupedCandles: { [key: number]: CandleWithVolume[] } = {};
  
  // Raggruppa le candele per intervallo di tempo
  candles.forEach(candle => {
    // Assicurati che il timestamp sia in millisecondi
    const candleTime = candle.time > 10000000000 ? candle.time : candle.time * 1000;
    const intervalStart = getIntervalStartTime(candleTime, intervalMinutes);
    
    if (!groupedCandles[intervalStart]) {
      groupedCandles[intervalStart] = [];
    }
    
    groupedCandles[intervalStart].push({
      ...candle,
      time: candleTime // Assicurati che il tempo sia in millisecondi
    });
  });
  
  // Crea le candele aggregate
  const aggregatedCandles: CandleWithVolume[] = Object.entries(groupedCandles).map(([intervalStart, intervalCandles]) => {
    // Ordina le candele per timestamp
    intervalCandles.sort((a, b) => a.time - b.time);
    
    const firstCandle = intervalCandles[0];
    const lastCandle = intervalCandles[intervalCandles.length - 1];
    
    // Calcola i valori aggregati
    const open = firstCandle.open;
    const close = lastCandle.close;
    const high = Math.max(...intervalCandles.map(c => c.high));
    const low = Math.min(...intervalCandles.map(c => c.low));
    const volume = intervalCandles.reduce((sum, c) => sum + (c.volume || 0), 0);
    
    // Restituisci la candela aggregata
    return {
      time: parseInt(intervalStart),
      open,
      high,
      low,
      close,
      volume
    };
  });
  
  // Ordina le candele aggregate per timestamp
  return aggregatedCandles.sort((a, b) => a.time - b.time);
};
