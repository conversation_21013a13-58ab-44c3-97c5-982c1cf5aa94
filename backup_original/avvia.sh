#!/bin/bash

# Script per avviare, fermare o riavviare l'applicazione BTC Scalping
# Autore: Cascade

# Colori per output
VERDE='\033[0;32m'
ROSSO='\033[0;31m'
GIALLO='\033[0;33m'
BLU='\033[0;34m'
NC='\033[0m' # No Color

# Funzione per mostrare l'aiuto
mostra_aiuto() {
  echo -e "${BLU}=== Gestione Applicazione BTC Scalping ===${NC}"
  echo -e "Utilizzo: $0 [opzione]"
  echo -e "Opzioni:"
  echo -e "  ${VERDE}avvia${NC}     - Avvia entrambi i server (frontend e backend)"
  echo -e "  ${ROSSO}ferma${NC}     - Ferma tutti i processi node in esecuzione"
  echo -e "  ${GIALLO}riavvia${NC}  - Riavvia entrambi i server"
  echo -e "  ${BLU}stato${NC}     - Mostra lo stato dei server"
  echo -e "  ${BLU}aiuto${NC}      - Mostra questo messaggio di aiuto"
}

# Funzione per avviare i server
avvia_server() {
  echo -e "${VERDE}Avvio dell'applicazione BTC Scalping...${NC}"
  cd "$(dirname "$0")" || exit
  npm run dev
}

# Funzione per fermare i server
ferma_server() {
  echo -e "${ROSSO}Arresto di tutti i processi node...${NC}"
  npm run stop
  echo -e "${VERDE}Tutti i processi node sono stati terminati.${NC}"
}

# Funzione per riavviare i server
riavvia_server() {
  echo -e "${GIALLO}Riavvio dell'applicazione BTC Scalping...${NC}"
  ferma_server
  sleep 2
  avvia_server
}

# Funzione per controllare lo stato
controlla_stato() {
  echo -e "${BLU}Controllo dello stato dei server...${NC}"
  
  # Controlla se il server backend è in esecuzione
  if nc -z localhost 3004 >/dev/null 2>&1; then
    echo -e "${VERDE}Server backend: IN ESECUZIONE (porta 3004)${NC}"
  else
    echo -e "${ROSSO}Server backend: NON IN ESECUZIONE${NC}"
  fi
  
  # Controlla se il server frontend è in esecuzione
  if nc -z localhost 5173 >/dev/null 2>&1; then
    echo -e "${VERDE}Server frontend: IN ESECUZIONE (porta 5173)${NC}"
  else
    echo -e "${ROSSO}Server frontend: NON IN ESECUZIONE${NC}"
  fi
}

# Gestione dei parametri
case "$1" in
  "avvia")
    avvia_server
    ;;
  "ferma")
    ferma_server
    ;;
  "riavvia")
    riavvia_server
    ;;
  "stato")
    controlla_stato
    ;;
  "aiuto"|"")
    mostra_aiuto
    ;;
  *)
    echo -e "${ROSSO}Opzione non valida: $1${NC}"
    mostra_aiuto
    exit 1
    ;;
esac

exit 0
