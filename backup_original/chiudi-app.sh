#!/bin/bash

# Script per terminare i processi dell'applicazione BTC Scalping

# Colori per i messaggi
VERDE='\033[0;32m'
ROSSO='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${VERDE}===============================================${NC}"
echo -e "${VERDE}   CHIUSURA BTC SCALPING APP   ${NC}"
echo -e "${VERDE}===============================================${NC}"
echo

# Trova e termina i processi del server Node.js
echo -e "${VERDE}Ricerca dei processi server...${NC}"
SERVER_PIDS=$(ps aux | grep "node server/server.js" | grep -v grep | awk '{print $2}')

if [ -n "$SERVER_PIDS" ]; then
    echo -e "${VERDE}Terminazione dei processi server: $SERVER_PIDS${NC}"
    echo $SERVER_PIDS | xargs kill -15
    echo -e "${VERDE}Server terminato con successo.${NC}"
else
    echo -e "${ROSSO}Nessun processo server trovato.${NC}"
fi

# Trova e termina i processi Vite (frontend)
echo -e "${VERDE}Ricerca dei processi frontend...${NC}"
VITE_PIDS=$(ps aux | grep "vite" | grep -v grep | awk '{print $2}')

if [ -n "$VITE_PIDS" ]; then
    echo -e "${VERDE}Terminazione dei processi frontend: $VITE_PIDS${NC}"
    echo $VITE_PIDS | xargs kill -15
    echo -e "${VERDE}Frontend terminato con successo.${NC}"
else
    echo -e "${ROSSO}Nessun processo frontend trovato.${NC}"
fi

# Trova e termina qualsiasi processo concurrently rimasto attivo
CONCURRENTLY_PIDS=$(ps aux | grep "concurrently" | grep -v grep | awk '{print $2}')

if [ -n "$CONCURRENTLY_PIDS" ]; then
    echo -e "${VERDE}Terminazione dei processi concurrently: $CONCURRENTLY_PIDS${NC}"
    echo $CONCURRENTLY_PIDS | xargs kill -15
else
    echo -e "${ROSSO}Nessun processo concurrently trovato.${NC}"
fi

echo
echo -e "${VERDE}Tutti i processi dell'applicazione sono stati terminati.${NC}"
