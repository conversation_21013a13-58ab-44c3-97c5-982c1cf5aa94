// Script per filtrare le predizioni
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// Ottieni la directory corrente in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PREDICTIONS_FILE = path.join(__dirname, 'predictions.json');

async function filterPredictions() {
  try {
    // Leggi il file delle predizioni
    const data = await fs.readFile(PREDICTIONS_FILE, 'utf8');
    const predictions = JSON.parse(data);
    
    console.log(`Predizioni totali prima del filtro: ${predictions.length}`);
    
    // Calcola la data di inizio per "questa notte" (dalle 00:00 di oggi)
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const lastNight = today.getTime();
    
    // Filtra le predizioni per mantenere solo quelle recenti con timeframe 1h o senza timeframe specificato
    const filteredPredictions = predictions.filter(p => {
      // Se la predizione è recente (da questa notte)
      const isRecent = p.timestamp >= lastNight;
      
      // Se non c'è un timeframe specificato o se è 1h
      // Nota: verifico prima nelle proprietà della predizione, poi nel campo evaluation
      const hasCorrectTimeframe = 
        !p.timeframe || 
        p.timeframe === '1h' || 
        (p.evaluation && Object.keys(p.evaluation).includes('1h'));
      
      return isRecent && hasCorrectTimeframe;
    });
    
    console.log(`Predizioni mantenute dopo il filtro: ${filteredPredictions.length}`);
    
    // Salva le predizioni filtrate
    await fs.writeFile(PREDICTIONS_FILE, JSON.stringify(filteredPredictions, null, 2));
    console.log('File delle predizioni aggiornato con successo!');
    
  } catch (error) {
    console.error('Errore durante il filtraggio delle predizioni:', error);
  }
}

// Esegui la funzione
filterPredictions();
