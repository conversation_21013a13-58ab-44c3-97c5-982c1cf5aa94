/**
 * Definizione delle route API dell'applicazione BTC Scalping
 */

const express = require('express');
const { generaPredizione } = require('../controller/predizioneController');
const { valutaPredizioni } = require('../controller/valutazioneController');
const { leggiPredizioni } = require('../servizi/archiviazione');
const { calcolaMetriche } = require('../utils/metriche');

const router = express.Router();

/**
 * Route per generare una nuova predizione
 * POST /predict
 */
router.post('/predict', async (req, res) => {
  try {
    const { rsi, macdHist, emaTrend, bbPosition, price, modelType } = req.body;
    
    // Validazione dei dati di input
    if (!rsi || !macdHist || !emaTrend || !bbPosition || !price) {
      return res.status(400).json({ 
        error: 'Dati degli indicatori incompleti' 
      });
    }
    
    // Genera la predizione
    const risultato = await generaPredizione(
      { rsi, macdHist, emaTrend, bbPosition, price }, 
      modelType
    );
    
    res.json(risultato);
  } catch (errore) {
    console.error('Errore durante la generazione della predizione:', errore);
    res.status(500).json({ 
      error: `Errore durante la generazione della predizione: ${errore.message}` 
    });
  }
});

/**
 * Route per valutare le predizioni pendenti
 * POST /evaluate
 */
router.post('/evaluate', async (req, res) => {
  try {
    const timeframe = req.body.timeframe || '1h';
    const risultato = await valutaPredizioni(timeframe);
    res.json(risultato);
  } catch (errore) {
    console.error('Errore durante la valutazione delle predizioni:', errore);
    res.status(500).json({ 
      error: `Errore durante la valutazione delle predizioni: ${errore.message}` 
    });
  }
});

/**
 * Route per ottenere le metriche di accuratezza
 * GET /metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    // Ottieni il timeframe dalla query string, con fallback a '1h'
    const timeframe = req.query.timeframe || '1h';
    const predizioni = await leggiPredizioni();
    const metriche = calcolaMetriche(predizioni, timeframe);
    res.json(metriche);
  } catch (errore) {
    console.error('Errore durante il calcolo delle metriche:', errore);
    res.status(500).json({ 
      error: `Errore durante il calcolo delle metriche: ${errore.message}` 
    });
  }
});

/**
 * Route per ottenere tutte le predizioni
 * GET /predictions
 */
router.get('/predictions', async (req, res) => {
  try {
    const predizioni = await leggiPredizioni();
    res.json(predizioni);
  } catch (errore) {
    console.error('Errore durante il recupero delle predizioni:', errore);
    res.status(500).json({ 
      error: `Errore durante il recupero delle predizioni: ${errore.message}` 
    });
  }
});

module.exports = router;
