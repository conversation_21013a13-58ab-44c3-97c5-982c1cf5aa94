/**
 * Servizio per la costruzione dei prompt dinamici
 * Gestisce la creazione di prompt per i modelli AI utilizzando esempi dinamici o statici
 */

const {
  ESEMPI_PER_CATEGORIA,
  TIMEFRAME_ESEMPI,
  MAX_ETÀ_ESEMPI,
  MIN_ESEMPI_PER_CATEGORIA,
  TIPI_TREND
} = require('../config');

/**
 * Ottiene le predizioni verificate come corrette dal database
 * @param {Array} predizioni - Array di tutte le predizioni
 * @param {string} [tipoModello] - Tipo di modello per filtrare le predizioni
 * @returns {Object} Predizioni verificate divise per categoria di trend
 */
function ottieniPredizioniVerificate(predizioni, tipoModello) {
  const predizioniVerificate = {
    [TIPI_TREND.RIALZISTA]: [],
    [TIPI_TREND.RIBASSISTA]: [],
    [TIPI_TREND.LATERALE]: []
  };
  
  const now = Date.now();
  
  for (const predizione of predizioni) {
    // Verifica che la predizione abbia una valutazione per il timeframe specificato
    if (predizione.evaluation && 
        predizione.evaluation[TIMEFRAME_ESEMPI] && 
        predizione.evaluation[TIMEFRAME_ESEMPI].status === 'correct') {
      
      // Se è stato specificato un modello, filtra per quel modello
      if (tipoModello && predizione.modelUsed && predizione.modelUsed !== tipoModello) {
        continue;
      }
      
      // Verifica che la predizione non sia troppo vecchia
      if (now - predizione.timestamp > MAX_ETÀ_ESEMPI) {
        continue;
      }
      
      // Aggiungi la predizione alla categoria corrispondente
      if (predizioniVerificate[predizione.prediction]) {
        predizioniVerificate[predizione.prediction].push(predizione);
      }
    }
  }
  
  return predizioniVerificate;
}

/**
 * Seleziona un numero equilibrato di esempi per ogni categoria di trend
 * @param {Object} predizioniVerificate - Oggetto con predizioni verificate per categoria
 * @param {Object} esempiFissi - Esempi statici da usare come fallback
 * @returns {Object} - Esempi selezionati per ogni categoria
 */
function selezionaEsempiEquilibrati(predizioniVerificate, esempiFissi) {
  const esempiSelezionati = {
    [TIPI_TREND.RIALZISTA]: [],
    [TIPI_TREND.RIBASSISTA]: [],
    [TIPI_TREND.LATERALE]: []
  };
  
  // Per ogni categoria di trend
  for (const trend of Object.keys(predizioniVerificate)) {
    const predizioni = predizioniVerificate[trend];
    
    // Ordina per data, dalle più recenti alle meno recenti
    predizioni.sort((a, b) => b.timestamp - a.timestamp);
    
    // Seleziona fino a ESEMPI_PER_CATEGORIA predizioni
    const numEsempiDisponibili = predizioni.length;
    const numEsempiDaUsare = Math.min(numEsempiDisponibili, ESEMPI_PER_CATEGORIA);
    
    if (numEsempiDaUsare > 0) {
      esempiSelezionati[trend] = predizioni.slice(0, numEsempiDaUsare);
    }
  }
  
  // Verifica se abbiamo abbastanza esempi per ogni categoria
  const haAbbastanzaEsempi = Object.values(esempiSelezionati).every(
    esempi => esempi.length >= MIN_ESEMPI_PER_CATEGORIA
  );
  
  // Se non abbiamo abbastanza esempi, usa gli esempi fissi
  if (!haAbbastanzaEsempi) {
    console.log('Non ci sono abbastanza esempi verificati, utilizzo esempi fissi');
    return esempiFissi;
  }
  
  return esempiSelezionati;
}

/**
 * Formatta un esempio di predizione per il prompt
 * @param {Object} predizione - Oggetto predizione
 * @returns {string} - Esempio formattato per il prompt
 */
function formattaEsempio(predizione) {
  const { rsi, macdHist, emaTrend, bbPosition } = predizione.indicators;
  
  return `
Input:
- RSI: ${rsi}
- MACD Histogram: ${macdHist}
- EMA Trend: ${emaTrend}
- Bollinger Position: ${bbPosition}

Output: ${predizione.prediction}`;
}

/**
 * Costruisce un prompt dinamico utilizzando esempi verificati o esempi fissi
 * @param {Object} datiAttuali - Dati attuali per la predizione
 * @param {Object} esempiSelezionati - Esempi selezionati per ogni categoria
 * @param {Object} esempiFissi - Esempi fissi da usare come fallback
 * @returns {string} - Prompt completo
 */
function costruisciPromptDinamico(datiAttuali, esempiSelezionati, esempiFissi) {
  // Verifica se stiamo usando esempi dinamici o fissi
  const usaEsempiDinamici = Object.values(esempiSelezionati).some(esempi => esempi.length > 0);
  
  // Scegli gli esempi da utilizzare
  const esempiDaUsare = usaEsempiDinamici ? esempiSelezionati : esempiFissi;
  
  // Costruisci la parte degli esempi del prompt
  let esempiPrompt = '';
  
  // Aggiungi esempi per ogni categoria
  for (const trend of Object.keys(esempiDaUsare)) {
    const esempi = esempiDaUsare[trend];
    
    if (esempi && esempi.length > 0) {
      // Se sono esempi dinamici, formatta ogni predizione
      if (usaEsempiDinamici) {
        esempi.forEach(predizione => {
          esempiPrompt += formattaEsempio(predizione);
        });
      } else {
        // Altrimenti usa gli esempi fissi così come sono
        esempiPrompt += esempi.join('\n');
      }
    }
  }
  
  // Costruisci il prompt completo
  const prompt = `Sei un esperto di analisi tecnica di Bitcoin. Analizza questi indicatori e predici se il prezzo andrà UP (su), DOWN (giù) o rimarrà LATERAL (laterale) nelle prossime ore.
  
Ecco alcuni esempi di analisi:
${esempiPrompt}

Ora analizza questi nuovi dati e rispondi SOLO con UP, DOWN o LATERAL:
- RSI: ${datiAttuali.rsi}
- MACD Histogram: ${datiAttuali.macdHist}
- EMA Trend: ${datiAttuali.emaTrend}
- Bollinger Position: ${datiAttuali.bbPosition}`;

  return prompt;
}

/**
 * Esempi fissi per il prompt
 * Utilizzati come fallback quando non ci sono abbastanza esempi dinamici
 */
const esempiFissi = {
  [TIPI_TREND.RIALZISTA]: [
    `
Input:
- RSI: 65
- MACD Histogram: 0.0012
- EMA Trend: UP
- Bollinger Position: 0.8

Output: UP`,
    `
Input:
- RSI: 58
- MACD Histogram: 0.0008
- EMA Trend: UP
- Bollinger Position: 0.6

Output: UP`
  ],
  [TIPI_TREND.RIBASSISTA]: [
    `
Input:
- RSI: 32
- MACD Histogram: -0.0015
- EMA Trend: DOWN
- Bollinger Position: -0.7

Output: DOWN`,
    `
Input:
- RSI: 38
- MACD Histogram: -0.0009
- EMA Trend: DOWN
- Bollinger Position: -0.5

Output: DOWN`
  ],
  [TIPI_TREND.LATERALE]: [
    `
Input:
- RSI: 48
- MACD Histogram: 0.0002
- EMA Trend: NEUTRAL
- Bollinger Position: 0.1

Output: LATERAL`,
    `
Input:
- RSI: 52
- MACD Histogram: -0.0001
- EMA Trend: NEUTRAL
- Bollinger Position: -0.2

Output: LATERAL`
  ]
};

module.exports = {
  ottieniPredizioniVerificate,
  selezionaEsempiEquilibrati,
  formattaEsempio,
  costruisciPromptDinamico,
  esempiFissi
};
