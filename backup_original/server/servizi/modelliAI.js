/**
 * Servizio per la gestione dei modelli AI
 * Configura e gestisce le connessioni ai diversi modelli AI supportati
 */

const { OpenAI } = require('openai');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const { TIPI_MODELLO } = require('../config');
const dotenv = require('dotenv');

// Configurazione dotenv
dotenv.config({ path: './server/.env' });

// Inizializzazione dei client per i vari modelli AI
let clientDeepSeek = null;
let clientGemini = null;

/**
 * Inizializza i client per i modelli AI
 * @returns {Object} Oggetto con i client inizializzati
 */
function inizializzaClientiAI() {
  try {
    // Inizializza client DeepSeek
    if (process.env.deepseek_api_key) {
      clientDeepSeek = new OpenAI({
        apiKey: process.env.deepseek_api_key,
        baseURL: 'https://api.deepseek.com/v1',
      });
      console.log('Client DeepSeek inizializzato con successo');
    } else {
      console.warn('API key DeepSeek non trovata, client non inizializzato');
    }

    // Inizializza client Gemini
    if (process.env.GOOGLE_API_KEY) {
      clientGemini = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
      console.log('Client Gemini inizializzato con successo');
    } else {
      console.warn('API key Google non trovata, client Gemini non inizializzato');
    }

    return {
      clientDeepSeek,
      clientGemini
    };
  } catch (errore) {
    console.error('Errore durante l\'inizializzazione dei client AI:', errore);
    throw errore;
  }
}

/**
 * Ottiene il client per il modello specificato
 * @param {string} tipoModello - Tipo di modello AI da utilizzare
 * @returns {Object} Client per il modello specificato e nome del modello
 */
function ottieniClientModello(tipoModello) {
  let client = null;
  let nomeModello = '';
  
  switch(tipoModello) {
    case TIPI_MODELLO.DEEPSEEK:
      client = clientDeepSeek;
      nomeModello = 'deepseek-chat';
      break;
    case TIPI_MODELLO.GROK4:
      client = clientDeepSeek; // Utilizziamo OpenRouter tramite DeepSeek
      nomeModello = 'openrouter/xai/grok-4';
      break;
    case TIPI_MODELLO.GEMINI:
      client = clientGemini;
      nomeModello = 'gemini-1.5-pro';
      break;
    default:
      // Fallback a DeepSeek se il tipo di modello non è valido
      client = clientDeepSeek;
      nomeModello = 'deepseek-chat';
      console.warn(`Tipo di modello non valido: ${tipoModello}, utilizzo DeepSeek come fallback`);
  }
  
  return { client, nomeModello };
}

/**
 * Esegue una predizione utilizzando il modello AI specificato
 * @param {string} tipoModello - Tipo di modello AI da utilizzare
 * @param {string} prompt - Prompt da inviare al modello
 * @returns {Promise<string>} Testo della risposta del modello
 */
async function eseguiPredizione(tipoModello, prompt) {
  const { client, nomeModello } = ottieniClientModello(tipoModello);
  
  if (!client) {
    throw new Error(`Client per il modello ${tipoModello} non disponibile`);
  }
  
  try {
    let risposta = '';
    
    if (tipoModello === TIPI_MODELLO.GEMINI) {
      // Chiamata API per Gemini
      const model = client.getGenerativeModel({ model: nomeModello });
      const result = await model.generateContent(prompt);
      risposta = result.response.text();
    } else {
      // Chiamata API per DeepSeek o Grok4 (via OpenRouter)
      const completion = await client.chat.completions.create({
        model: nomeModello,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_tokens: 150
      });
      risposta = completion.choices[0].message.content;
    }
    
    return risposta;
  } catch (errore) {
    console.error(`Errore durante la chiamata al modello ${nomeModello}:`, errore);
    throw errore;
  }
}

module.exports = {
  inizializzaClientiAI,
  ottieniClientModello,
  eseguiPredizione,
  TIPI_MODELLO
};
