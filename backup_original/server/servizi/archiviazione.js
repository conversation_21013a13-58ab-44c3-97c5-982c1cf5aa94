/**
 * Servizio per la gestione dell'archiviazione delle predizioni
 * Fornisce funzioni per leggere e scrivere le predizioni su file
 */

const fs = require('fs').promises;
const path = require('path');
const { PERCORSO_FILE_PREDIZIONI } = require('../config');

/**
 * Legge le predizioni dal file di archiviazione
 * @returns {Promise<Array>} Array di predizioni
 */
async function leggiPredizioni() {
  try {
    // Assicura che la directory esista
    const dir = path.dirname(PERCORSO_FILE_PREDIZIONI);
    await fs.mkdir(dir, { recursive: true });
    
    // Legge il file delle predizioni
    const data = await fs.readFile(PERCORSO_FILE_PREDIZIONI, 'utf8');
    return JSON.parse(data || '[]');
  } catch (errore) {
    // Se il file non esiste, restituisce un array vuoto
    if (errore.code === 'ENOENT') {
      return [];
    }
    // Altrimenti propaga l'errore
    throw new Error(`Errore durante la lettura delle predizioni: ${errore.message}`);
  }
}

/**
 * Scrive le predizioni nel file di archiviazione
 * @param {Array} predizioni - Array di predizioni da salvare
 * @returns {Promise<void>}
 */
async function scriviPredizioni(predizioni) {
  try {
    // Assicura che la directory esista
    const dir = path.dirname(PERCORSO_FILE_PREDIZIONI);
    await fs.mkdir(dir, { recursive: true });
    
    // Scrive il file delle predizioni
    await fs.writeFile(PERCORSO_FILE_PREDIZIONI, JSON.stringify(predizioni, null, 2));
  } catch (errore) {
    throw new Error(`Errore durante la scrittura delle predizioni: ${errore.message}`);
  }
}

module.exports = {
  leggiPredizioni,
  scriviPredizioni
};
