/**
 * Configurazione generale dell'applicazione BTC Scalping
 * Contiene tutte le costanti e le impostazioni utilizzate nell'applicazione
 */

// Configurazione del server
const PORTA_SERVER = process.env.PORT || 3004;
const PERCORSO_FILE_PREDIZIONI = './server/data/predictions.json';

// Soglie e timeframe per la valutazione delle predizioni
const SOGLIA_PROFITTO = 0.005; // 0.5%
const TIMEFRAME_VALUTAZIONE = { // in millisecondi
  '5m': 5 * 60 * 1000,
  '10m': 10 * 60 * 1000,
  '15m': 15 * 60 * 1000,
  '1h': 60 * 60 * 1000,
  '4h': 4 * 60 * 60 * 1000,
  '8h': 8 * 60 * 60 * 1000,
  '24h': 24 * 60 * 60 * 1000,
};

// Configurazione per il sistema di esempi dinamici
const ESEMPI_PER_CATEGORIA = 5;  // Numero di esempi da utilizzare per ogni categoria di trend
const TIMEFRAME_ESEMPI = '4h';    // Timeframe da utilizzare per selezionare esempi verificati
const MAX_ETÀ_ESEMPI = 30 * 24 * 60 * 60 * 1000; // 30 giorni in millisecondi
const MIN_ESEMPI_PER_CATEGORIA = 3; // Numero minimo di esempi richiesti per usare esempi dinamici

// Tipi di modelli AI supportati
const TIPI_MODELLO = {
  DEEPSEEK: 'deepseek',
  GROK4: 'grok4',
  GEMINI: 'gemini'
};

// Tipi di trend supportati
const TIPI_TREND = {
  RIALZISTA: 'UP',
  RIBASSISTA: 'DOWN',
  LATERALE: 'LATERAL'
};

// Esporta tutte le configurazioni
module.exports = {
  PORTA_SERVER,
  PERCORSO_FILE_PREDIZIONI,
  SOGLIA_PROFITTO,
  TIMEFRAME_VALUTAZIONE,
  ESEMPI_PER_CATEGORIA,
  TIMEFRAME_ESEMPI,
  MAX_ETÀ_ESEMPI,
  MIN_ESEMPI_PER_CATEGORIA,
  TIPI_MODELLO,
  TIPI_TREND
};
