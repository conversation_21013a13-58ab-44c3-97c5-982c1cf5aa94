/**
 * Utilità per il calcolo delle metriche di accuratezza delle predizioni
 */

/**
 * Calcola le metriche di accuratezza delle predizioni
 * @param {Array} predizioni - Array di tutte le predizioni
 * @param {string} [timeframeRiferimento='1h'] - Timeframe da utilizzare come riferimento per le metriche globali
 * @returns {Object} Metriche calcolate
 */
function calcolaMetriche(predizioni, timeframeRiferimento = '1h') {
  const metriche = {
    totale: 0,
    corrette: 0,
    incorrette: 0,
    pending: 0,
    accuratezza: 0,
    perTimeframe: {},
    perTrend: {
      UP: { totale: 0, corrette: 0, accuratezza: 0 },
      DOWN: { totale: 0, corrette: 0, accuratezza: 0 },
      LATERAL: { totale: 0, corrette: 0, accuratezza: 0 }
    }
  };

  // Inizializza metriche per timeframe
  const timeframes = ['5m', '10m', '15m', '1h', '4h', '8h', '24h'];
  timeframes.forEach(tf => {
    metriche.perTimeframe[tf] = {
      totale: 0,
      corrette: 0,
      incorrette: 0,
      pending: 0,
      accuratezza: 0
    };
  });

  // Calcola metriche
  predizioni.forEach(p => {
    if (p.prediction === 'ERROR') return; // Ignora le predizioni con errori
    
    metriche.totale++;
    
    // Aggiorna conteggio per trend
    if (metriche.perTrend[p.prediction]) {
      metriche.perTrend[p.prediction].totale++;
    }
    
    // Analizza ogni timeframe
    if (p.evaluation) {
      Object.entries(p.evaluation).forEach(([tf, eval]) => {
        if (!metriche.perTimeframe[tf]) return;
        
        metriche.perTimeframe[tf].totale++;
        
        if (eval.status === 'correct') {
          metriche.perTimeframe[tf].corrette++;
          
          // Aggiorna conteggio per trend
          if (metriche.perTrend[p.prediction]) {
            metriche.perTrend[p.prediction].corrette++;
          }
        } else if (eval.status === 'incorrect') {
          metriche.perTimeframe[tf].incorrette++;
        } else if (eval.status === 'pending') {
          metriche.perTimeframe[tf].pending++;
        }
        
        // Calcola accuratezza per timeframe
        const valutate = metriche.perTimeframe[tf].corrette + metriche.perTimeframe[tf].incorrette;
        metriche.perTimeframe[tf].accuratezza = valutate > 0 
          ? (metriche.perTimeframe[tf].corrette / valutate * 100).toFixed(2) 
          : 0;
      });
    }
  });
  
  // Calcola accuratezza globale basata sul timeframe di riferimento specificato
  // Se il timeframe specificato non esiste nei dati, usa '1h' come fallback
  const tfRiferimento = metriche.perTimeframe[timeframeRiferimento] ? timeframeRiferimento : '1h';
  
  // Aggiungiamo il timeframe di riferimento alle metriche per riferimento nel frontend
  metriche.timeframeRiferimento = tfRiferimento;
  
  const valutate = metriche.perTimeframe[tfRiferimento].corrette + 
                   metriche.perTimeframe[tfRiferimento].incorrette;
  
  metriche.corrette = metriche.perTimeframe[tfRiferimento].corrette;
  metriche.incorrette = metriche.perTimeframe[tfRiferimento].incorrette;
  metriche.pending = metriche.perTimeframe[tfRiferimento].pending;
  metriche.accuratezza = valutate > 0 
    ? (metriche.corrette / valutate * 100).toFixed(2) 
    : 0;
  
  // Calcola accuratezza per trend
  Object.keys(metriche.perTrend).forEach(trend => {
    const trendData = metriche.perTrend[trend];
    trendData.accuratezza = trendData.totale > 0 
      ? (trendData.corrette / trendData.totale * 100).toFixed(2) 
      : 0;
  });
  
  return metriche;
}

module.exports = {
  calcolaMetriche
};
