/**
 * Sistema di logging centralizzato per l'applicazione BTC Scalping
 * Fornisce funzioni per registrare messaggi con diversi livelli di gravità
 */

// Colori per i messaggi di log
const COLORI = {
  INFO: '\x1b[36m%s\x1b[0m',    // Ciano
  SUCCESSO: '\x1b[32m%s\x1b[0m', // Verde
  AVVISO: '\x1b[33m%s\x1b[0m',   // Giallo
  ERRORE: '\x1b[31m%s\x1b[0m',   // Rosso
  DEBUG: '\x1b[35m%s\x1b[0m'     // Magenta
};

/**
 * Formatta un messaggio di log con timestamp
 * @param {string} livello - Livello del log (INFO, SUCCESSO, AVVISO, ERRORE, DEBUG)
 * @param {string} messaggio - Messaggio da loggare
 * @returns {string} - Messaggio formattato
 */
function formattaMessaggio(livello, messaggio) {
  const timestamp = new Date().toISOString();
  return `[${timestamp}] [${livello}] ${messaggio}`;
}

/**
 * Registra un messaggio informativo
 * @param {string} messaggio - Messaggio da loggare
 * @param {Object} [dati] - Dati aggiuntivi da loggare
 */
function info(messaggio, dati = null) {
  const messaggioFormattato = formattaMessaggio('INFO', messaggio);
  console.log(COLORI.INFO, messaggioFormattato);
  if (dati) console.log(dati);
}

/**
 * Registra un messaggio di successo
 * @param {string} messaggio - Messaggio da loggare
 * @param {Object} [dati] - Dati aggiuntivi da loggare
 */
function successo(messaggio, dati = null) {
  const messaggioFormattato = formattaMessaggio('SUCCESSO', messaggio);
  console.log(COLORI.SUCCESSO, messaggioFormattato);
  if (dati) console.log(dati);
}

/**
 * Registra un messaggio di avviso
 * @param {string} messaggio - Messaggio da loggare
 * @param {Object} [dati] - Dati aggiuntivi da loggare
 */
function avviso(messaggio, dati = null) {
  const messaggioFormattato = formattaMessaggio('AVVISO', messaggio);
  console.log(COLORI.AVVISO, messaggioFormattato);
  if (dati) console.log(dati);
}

/**
 * Registra un messaggio di errore
 * @param {string} messaggio - Messaggio da loggare
 * @param {Error} [errore] - Oggetto errore da loggare
 */
function errore(messaggio, errore = null) {
  const messaggioFormattato = formattaMessaggio('ERRORE', messaggio);
  console.error(COLORI.ERRORE, messaggioFormattato);
  if (errore) {
    console.error(errore.stack || errore);
  }
}

/**
 * Registra un messaggio di debug (solo in ambiente di sviluppo)
 * @param {string} messaggio - Messaggio da loggare
 * @param {Object} [dati] - Dati aggiuntivi da loggare
 */
function debug(messaggio, dati = null) {
  // Logga solo se non siamo in produzione
  if (process.env.NODE_ENV !== 'production') {
    const messaggioFormattato = formattaMessaggio('DEBUG', messaggio);
    console.log(COLORI.DEBUG, messaggioFormattato);
    if (dati) console.log(dati);
  }
}

module.exports = {
  info,
  successo,
  avviso,
  errore,
  debug
};
