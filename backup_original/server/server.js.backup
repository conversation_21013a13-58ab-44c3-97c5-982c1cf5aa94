import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import OpenAI from 'openai';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Configurazione delle variabili di percorso e caricamento .env dalla cartella server
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '.env') });

const app = express();
const port = 3004;

// Verifica la presenza delle chiavi API necessarie
if (!process.env.deepseek_api_key) {
  console.error('ERRORE: La variabile d\'ambiente deepseek_api_key non è stata impostata.');
  // process.exit(1); // Si potrebbe terminare il processo se la chiave è essenziale
}

if (!process.env.OPENROUTER_API_KEY) {
  console.error('ERRORE: La variabile d\'ambiente OPENROUTER_API_KEY non è stata impostata.');
  // process.exit(1); // Si potrebbe terminare il processo se la chiave è essenziale
}

if (!process.env.GOOGLE_API_KEY) {
  console.error('ERRORE: La variabile d\'ambiente GOOGLE_API_KEY non è stata impostata.');
  // process.exit(1); // Si potrebbe terminare il processo se la chiave è essenziale
}

// Inizializza il client DeepSeek con configurazione API compatibile OpenAI
const deepseekClient = process.env.deepseek_api_key ? new OpenAI({
  apiKey: process.env.deepseek_api_key,
  baseURL: 'https://api.deepseek.com/v1',
}) : null;

// Inizializza il client OpenRouter per accedere a Grok 4
const openrouterClient = process.env.OPENROUTER_API_KEY ? new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
  defaultHeaders: {
    'HTTP-Referer': 'http://localhost:3004', // URL dell'applicazione
    'X-Title': 'BTC Scalping App'             // Nome dell'applicazione
  }
}) : null;

// Inizializza il client Google AI per accedere a Gemini
const googleClient = process.env.GOOGLE_API_KEY ? new GoogleGenerativeAI(process.env.GOOGLE_API_KEY) : null;
const geminiModel = googleClient ? googleClient.getGenerativeModel({ model: 'gemini-1.5-pro' }) : null;

// Configurazione per richieste DeepSeek R1
const deepseekModel = deepseekClient ? {
  generateContent: async (prompt) => {
    try {
      // Parametri ottimizzati per predizioni precise
      const response = await deepseekClient.chat.completions.create({
        model: 'deepseek-chat',  // Corrisponde a DeepSeek-V3-0324
        messages: [
          { role: 'system', content: 'Sei un esperto analista di trading specializzato in scalping su BTC/USDT.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.4,         // Equilibrio ottimale tra creatività e precisione
        top_p: 0.8,               // Controllo della distribuzione di probabilità
        max_tokens: 10,           // Sufficiente per risposta singola
      });

      // Crea un formato di risposta compatibile con l'implementazione esistente
      return { 
        response: { 
          text: () => response.choices[0].message.content.trim() 
        } 
      };
    } catch (error) {
      console.error('Errore durante la chiamata all\'API DeepSeek:', error);
      throw error;
    }
  }
} : null;

// Configurazione per richieste a Grok 4 via OpenRouter
const grokModel = openrouterClient ? {
  generateContent: async (prompt) => {
    try {
      // Parametri ottimizzati per predizioni precise con Grok 4
      const response = await openrouterClient.chat.completions.create({
        model: 'xai/grok-4',  // Modello Grok 4 tramite OpenRouter
        messages: [
          { role: 'system', content: 'Sei un esperto analista di trading specializzato in scalping su BTC/USDT.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.4,         // Equilibrio ottimale tra creatività e precisione
        top_p: 0.8,               // Controllo della distribuzione di probabilità
        max_tokens: 10,           // Sufficiente per risposta singola
      });

      // Crea un formato di risposta compatibile con l'implementazione esistente
      return { 
        response: { 
          text: () => response.choices[0].message.content.trim() 
        } 
      };
    } catch (error) {
      console.error('Errore durante la chiamata a Grok 4 via OpenRouter:', error);
      throw error;
    }
  }
} : null;

// Configurazione per richieste a Gemini via Google AI
const geminiModelWrapper = geminiModel ? {
  generateContent: async (prompt) => {
    try {
      // Parametri ottimizzati per predizioni precise con Gemini
      const result = await geminiModel.generateContent({
        contents: [{
          role: 'user',
          parts: [{
            text: `[SISTEMA] Sei un esperto analista di trading specializzato in scalping su BTC/USDT.\n\n[UTENTE] ${prompt}`
          }]
        }],
        generationConfig: {
          temperature: 0.4,
          topP: 0.8,
          maxOutputTokens: 10,
        }
      });
      
      const response = await result.response;
      
      // Crea un formato di risposta compatibile con l'implementazione esistente
      return { 
        response: { 
          text: () => response.text().trim() 
        } 
      };
    } catch (error) {
      console.error('Errore durante la chiamata a Gemini via Google AI:', error);
      throw error;
    }
  }
} : null;

// Modello predefinito da utilizzare (DeepSeek per compatibilità con il codice esistente)
const model = deepseekModel;

app.use(cors());
app.use(express.json());

// Definizione dei percorsi dei file di predizione
const PREDICTIONS_FILE = path.join(__dirname, 'predictions.json');
const PREDICTIONS_TEMP_FILE = path.join(__dirname, 'predictions.temp.json');
const PREDICTIONS_BACKUP_FILE = path.join(__dirname, 'predictions.backup.json');

// --- Funzioni di Utilità per le Metriche ---

/**
 * Verifica se una stringa è un JSON valido
 * @param {string} jsonString - La stringa JSON da validare
 * @returns {boolean} - true se il JSON è valido, false altrimenti
 */
function isValidJSON(jsonString) {
  try {
    JSON.parse(jsonString);
    return true;
  } catch (e) {
    return false;
  }
}

/**
 * Copia un file da origine a destinazione
 * @param {string} source - File di origine
 * @param {string} destination - File di destinazione
 */
async function copyFile(source, destination) {
  try {
    await fs.copyFile(source, destination);
  } catch (error) {
    console.error(`Errore copiando ${source} a ${destination}:`, error);
  }
}

/**
 * Tenta di ripristinare il file predictions.json dal backup in caso di corruzione
 * @returns {boolean} - true se il ripristino è riuscito, false altrimenti
 */
async function restoreFromBackup() {
  console.log('Tentativo di ripristino dal file di backup...');
  try {
    // Verifica se esiste il file di backup
    try {
      await fs.access(PREDICTIONS_BACKUP_FILE);
    } catch (e) {
      console.error('Nessun file di backup trovato per il ripristino');
      return false;
    }
    
    // Leggi il file di backup e verifica che sia un JSON valido
    const backupData = await fs.readFile(PREDICTIONS_BACKUP_FILE, 'utf-8');
    if (!isValidJSON(backupData)) {
      console.error('Il file di backup è corrotto');
      return false;
    }
    
    // Copia il backup sul file principale
    await fs.copyFile(PREDICTIONS_BACKUP_FILE, PREDICTIONS_FILE);
    console.log('Ripristino dal backup completato con successo');
    return true;
  } catch (error) {
    console.error('Errore durante il ripristino dal backup:', error);
    return false;
  }
}

/**
 * Legge il file delle predizioni e gestisce i casi di corruzione
 * @returns {Array} - Array delle predizioni
 */
async function readPredictions() {
  try {
    // Verifica se esiste il file
    try {
      await fs.access(PREDICTIONS_FILE);
    } catch (e) {
      // Se il file non esiste, ritorna un array vuoto
      return [];
    }
    
    // Leggi il file e prova a parsare il JSON
    const data = await fs.readFile(PREDICTIONS_FILE, 'utf-8');
    
    // Verifica che sia un JSON valido
    if (!isValidJSON(data)) {
      console.error('Il file predictions.json è corrotto, tentativo di ripristino...');
      // Tenta il ripristino dal backup
      const restored = await restoreFromBackup();
      if (restored) {
        // Riprova a leggere dopo il ripristino
        return await readPredictions();
      } else {
        console.error('Impossibile ripristinare dal backup, ritorno array vuoto');
        return [];
      }
    }
    
    return JSON.parse(data);
  } catch (error) {
    console.error('Errore leggendo predictions.json:', error);
    // Tenta il ripristino dal backup in caso di errore
    const restored = await restoreFromBackup();
    if (restored) {
      // Riprova a leggere dopo il ripristino
      return await readPredictions();
    }
    return [];
  }
}

/**
 * Scrive i dati delle predizioni in modo atomico con backup
 * @param {Array} data - I dati delle predizioni da salvare
 */
async function writePredictions(data) {
  // Crea un lock file per evitare scritture simultanee
  const lockFile = path.join(__dirname, 'predictions.lock');
  
  try {
    // Verifica che i dati siano un array
    if (!Array.isArray(data)) {
      throw new Error('I dati forniti non sono un array');
    }
    
    // Conversione in JSON
    const jsonData = JSON.stringify(data, null, 2);
    
    // Verifica che la conversione abbia prodotto un JSON valido
    if (!isValidJSON(jsonData)) {
      throw new Error('La conversione ha prodotto JSON non valido');
    }
    
    // Crea un backup prima di scrivere (se il file esiste)
    try {
      await fs.access(PREDICTIONS_FILE);
      await copyFile(PREDICTIONS_FILE, PREDICTIONS_BACKUP_FILE);
    } catch (e) {
      // Il file non esiste, nessun backup necessario per la prima scrittura
    }
    
    // Scrivi prima nel file temporaneo
    await fs.writeFile(PREDICTIONS_TEMP_FILE, jsonData, 'utf-8');
    
    // Verifica che il file temporaneo sia valido
    const tempData = await fs.readFile(PREDICTIONS_TEMP_FILE, 'utf-8');
    if (!isValidJSON(tempData)) {
      throw new Error('Il file temporaneo scritto non è un JSON valido');
    }
    
    // Rinomina atomicamente il file temporaneo sul file finale
    await fs.rename(PREDICTIONS_TEMP_FILE, PREDICTIONS_FILE);
    
    console.log(`Salvataggio completato: ${data.length} predizioni scritte con successo`);
  } catch (error) {
    console.error('Errore scrivendo su predictions.json:', error);
    // In caso di errore, tenta di ripristinare dal backup
    await restoreFromBackup();
  } finally {
    // Rimuovi il lock file
    try {
      await fs.unlink(lockFile);
    } catch (e) {
      // Ignora se il file non esiste
    }
  }
}

// --- Costanti di Configurazione ---
const PROFIT_THRESHOLD = 0.005; // 0.5%
const EVALUATION_TIMEFRAMES = { // in millisecondi
  '1h': 60 * 60 * 1000,
  '4h': 4 * 60 * 60 * 1000,
  '8h': 8 * 60 * 60 * 1000,
  '24h': 24 * 60 * 60 * 1000,
};

// --- Costanti per il sistema di esempi dinamici ---
const ESEMPI_PER_CATEGORIA = 5;  // Numero di esempi da utilizzare per ogni categoria di trend
const TIMEFRAME_ESEMPI = '4h';    // Timeframe da utilizzare per selezionare esempi verificati
const MAX_ETÀ_ESEMPI = 30 * 24 * 60 * 60 * 1000; // 30 giorni in millisecondi

/**
 * Ottiene predizioni verificate dal database delle predizioni
 * @param {Array} predizioni - Array di tutte le predizioni
 * @param {string} timeframe - Timeframe da considerare per la valutazione ('1h', '4h', etc)
 * @param {string} modelType - Tipo di modello utilizzato per filtrare predizioni
 * @returns {Object} - Oggetto con predizioni suddivise per trend e verificate come corrette
 */
async function ottieniPredizioniVerificate(predizioni, timeframe = TIMEFRAME_ESEMPI, modelType = null) {
  const now = Date.now();
  const predizioniVerificate = {
    UP: [],
    DOWN: [],
    LATERAL: []
  };
  
  // Filtra predizioni che hanno una valutazione completata
  for (const predizione of predizioni) {
    // Verifica che la predizione abbia una valutazione per il timeframe specificato
    if (predizione.evaluation && 
        predizione.evaluation[timeframe] && 
        predizione.evaluation[timeframe].status === 'correct') {
      
      // Se è stato specificato un modello, filtra per quel modello
      if (modelType && predizione.modelUsed && predizione.modelUsed !== modelType) {
        continue;
      }
      
      // Verifica che la predizione non sia troppo vecchia
      if (now - predizione.timestamp > MAX_ETÀ_ESEMPI) {
        continue;
      }
      
      // Aggiungi la predizione alla categoria corrispondente
      if (predizioniVerificate[predizione.prediction]) {
        predizioniVerificate[predizione.prediction].push(predizione);
      }
    }
  }
  
  return predizioniVerificate;
}

/**
 * Seleziona un numero equilibrato di esempi per ogni categoria di trend
 * @param {Object} predizioniVerificate - Oggetto con predizioni verificate per categoria
 * @param {Object} esempiFissi - Esempi statici da usare come fallback
 * @returns {Object} - Esempi selezionati per ogni categoria
 */
function selezionaEsempiEquilibrati(predizioniVerificate, esempiFissi) {
  const esempiSelezionati = {
    UP: [],
    DOWN: [],
    LATERAL: []
  };
  
  // Per ogni categoria di trend
  for (const trend of Object.keys(predizioniVerificate)) {
    const predizioni = predizioniVerificate[trend];
    
    // Ordina per data, dalle più recenti alle meno recenti
    predizioni.sort((a, b) => b.timestamp - a.timestamp);
    
    // Seleziona fino a ESEMPI_PER_CATEGORIA predizioni
    const numEsempiDisponibili = predizioni.length;
    const numEsempiDaUsare = Math.min(numEsempiDisponibili, ESEMPI_PER_CATEGORIA);
    
    if (numEsempiDaUsare > 0) {
      esempiSelezionati[trend] = predizioni.slice(0, numEsempiDaUsare);
    }
  }
  
  // Verifica se abbiamo abbastanza esempi per ogni categoria
  // Se non ne abbiamo abbastanza, usiamo gli esempi fissi
  for (const trend of Object.keys(esempiSelezionati)) {
    if (esempiSelezionati[trend].length < 3) { // Richiediamo almeno 3 esempi per categoria
      esempiSelezionati[trend] = []; // Svuota gli esempi dinamici per questa categoria
      // Gli esempi fissi verranno usati al loro posto nella costruzione del prompt
    }
  }
  
  return esempiSelezionati;
}

/**
 * Costruisce un esempio formattato a partire da una predizione verificata
 * @param {Object} predizione - La predizione verificata
 * @param {number} indice - Indice dell'esempio per la numerazione
 * @returns {string} - Esempio formattato per il prompt
 */
function formattaEsempio(predizione, indice) {
  return `Esempio ${indice}:\n` +
         `- Dati: RSI=${predizione.indicators.rsi.toFixed(1)}, ` +
         `Istogramma MACD=${predizione.indicators.macdHist.toFixed(1)}, ` +
         `Trend EMA=${predizione.indicators.emaTrend}, ` +
         `Posizione BB=${predizione.indicators.bbPosition.toFixed(2)}\n` +
         `- Previsione: ${predizione.prediction}\n`;
}

/**
 * Costruisce il prompt dinamico utilizzando esempi reali verificati
 * @param {Object} datiAttuali - I dati attuali per cui si chiede una predizione
 * @param {Object} esempiSelezionati - Esempi selezionati per ogni categoria
 * @param {Object} esempiFissi - Esempi statici da usare come fallback
 * @returns {string} - Il prompt completo
 */
function costruisciPromptDinamico(datiAttuali, esempiSelezionati, esempiFissi = null) {
  let prompt = `Sei un esperto analista di trading specializzato in scalping su BTC/USDT. Analizza i seguenti dati di mercato da candele a 1 ora e fornisci una previsione secca per il trend nelle prossime 4-8 ore, basandoti sugli esempi forniti.\n\n--- Esempi Bilanciati ---\n\n`;
  
  // Funzione helper per aggiungere esempi al prompt
  const aggiungiEsempi = (categoria, titolo) => {
    prompt += `# Esempi di trend ${titolo} - Timeframe 1h:\n\n`;
    
    // Se abbiamo esempi dinamici per questa categoria, li usiamo
    if (esempiSelezionati[categoria] && esempiSelezionati[categoria].length > 0) {
      let indiceEsempio = 1;
      for (const esempio of esempiSelezionati[categoria]) {
        prompt += formattaEsempio(esempio, indiceEsempio) + '\n';
        indiceEsempio++;
      }
    } 
    // Altrimenti usiamo gli esempi fissi
    else if (esempiFissi && esempiFissi[categoria]) {
      prompt += esempiFissi[categoria].join('\n') + '\n\n';
    }
  };
  
  // Aggiungi esempi per ogni categoria
  aggiungiEsempi('UP', 'RIALZISTA (UP)');
  aggiungiEsempi('DOWN', 'RIBASSISTA (DOWN)');
  aggiungiEsempi('LATERAL', 'LATERALE');
  
  // Aggiungi i dati attuali al prompt
  prompt += `--- Dati Attuali da Analizzare ---\n\n` +
            `- Prezzo Corrente (USDT): ${datiAttuali.price.toFixed(2)}\n` +
            `- RSI (14): ${datiAttuali.rsi.toFixed(2)}\n` +
            `- Istogramma MACD: ${datiAttuali.macdHist.toFixed(4)}\n` +
            `- Trend basato su EMA: ${datiAttuali.emaTrend}\n` +
            `- Posizione nelle Bande di Bollinger (0-1): ${datiAttuali.bbPosition.toFixed(2)}\n\n` +
            `Basandoti sugli esempi e sui dati attuali, il trend più probabile è 'UP', 'DOWN', o 'LATERAL'?\n\n` +
            `Rispondi solo e unicamente con una delle tre parole: UP, DOWN, LATERAL. Non aggiungere alcuna altra parola o punteggiatura.`;
  
  return prompt;
}

function calculateMetrics(predictions, timeframe = '1h') {
    const evaluated = predictions.filter(p => p.evaluation && p.evaluation[timeframe] && p.evaluation[timeframe].status !== 'pending');
    if (evaluated.length === 0) {
        return { accuracy: 0, precision: 0, recall: 0, f1Score: 0, recentAccuracy: 0, totalPredictions: predictions.length, timeframe };
    }
    
    const getStatus = (p) => p.evaluation[timeframe]?.status || 'pending';

    const totalEvaluated = evaluated.length;
    const correctPredictions = evaluated.filter(p => getStatus(p) === 'correct').length;
    
    const truePositives = evaluated.filter(p => p.prediction === 'UP' && getStatus(p) === 'correct').length;
    const falsePositives = evaluated.filter(p => p.prediction === 'UP' && getStatus(p) === 'incorrect').length;
    const trueNegatives = evaluated.filter(p => p.prediction === 'DOWN' && getStatus(p) === 'correct').length;
    const falseNegatives = evaluated.filter(p => p.prediction === 'DOWN' && getStatus(p) === 'incorrect').length;

    const accuracy = (correctPredictions / totalEvaluated) * 100;
    const precision = truePositives + falsePositives > 0 ? (truePositives / (truePositives + falsePositives)) * 100 : 0;
    const recall = truePositives + falseNegatives > 0 ? (truePositives / (truePositives + falseNegatives)) * 100 : 0;
    const f1Score = precision + recall > 0 ? 2 * (precision * recall) / (precision + recall) : 0;

    const recentEvaluated = evaluated.slice(-20);
    const recentCorrect = recentEvaluated.filter(p => getStatus(p) === 'correct').length;
    const recentAccuracy = recentEvaluated.length > 0 ? (recentCorrect / recentEvaluated.length) * 100 : 0;

    return {
        accuracy,
        precision,
        recall,
        f1Score,
        recentAccuracy,
        totalPredictions: predictions.length,
        timeframe
    };
}

// ... (rest of the code remains the same)

app.post('/evaluate', async (req, res) => {
    let allPredictions = await readPredictions();
    const predictionsToEvaluate = allPredictions.filter(p => 
        p.evaluation && Object.values(p.evaluation).some(e => e.status === 'pending')
    );
    let updatedCount = 0;

    const timeframe = req.body.timeframe || '1h';

    if (predictionsToEvaluate.length > 0) {
        try {
            const response = await fetch('https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT');
            if (!response.ok) {
                // Se la risposta non è ok, lancia un errore specifico
                throw new Error(`Errore API Binance: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            const currentPrice = parseFloat(data.price);
            const now = Date.now();

            for (const p of predictionsToEvaluate) {
                const priceAtPrediction = p.priceAtPrediction;
                let actualTrend;
                
                if (currentPrice > priceAtPrediction * (1 + PROFIT_THRESHOLD)) {
                    actualTrend = 'UP';
                } else if (currentPrice < priceAtPrediction * (1 - PROFIT_THRESHOLD)) {
                    actualTrend = 'DOWN';
                } else {
                    actualTrend = 'LATERAL';
                }

                for (const tf in p.evaluation) {
                    const tfMillis = EVALUATION_TIMEFRAMES[tf];
                    if (p.evaluation[tf].status === 'pending' && now - p.timestamp > tfMillis) {
                        p.evaluation[tf].status = p.prediction === actualTrend ? 'correct' : 'incorrect';
                        p.evaluation[tf].actualTrend = actualTrend;
                        p.evaluation[tf].evaluatedAt = now;
                        p.evaluation[tf].priceAtEvaluation = currentPrice;
                        updatedCount++;
                    }
                }
            }

            if (updatedCount > 0) {
                await writePredictions(allPredictions);
            }
        } catch (error) {
            // Log dell'errore più dettagliato e invio di una risposta di errore
            console.error("Errore critico durante la valutazione (fetch failed?):", error);
            return res.status(500).json({ 
                error: 'Impossibile contattare l\'API di Binance per la valutazione.',
                details: error.message 
            });
        }
    }
    
    // Se tutto va bene (o non c'era nulla da valutare), calcola e invia le metriche
    const updatedMetrics = calculateMetrics(allPredictions, timeframe);
    res.json({ 
        message: `${updatedCount} valutazioni aggiornate.`,
        metrics: updatedMetrics 
    });
});

// Esempi statici predefiniti per essere usati come fallback
const esempiFissi = {
  UP: [
    "Esempio 1:\n- Dati: RSI=72.4, Istogramma MACD=18.5, Trend EMA=BULLISH, Posizione BB=0.89\n- Previsione: UP",
    "Esempio 2:\n- Dati: RSI=68.7, Istogramma MACD=15.2, Trend EMA=BULLISH, Posizione BB=0.82\n- Previsione: UP",
    "Esempio 3:\n- Dati: RSI=65.3, Istogramma MACD=12.8, Trend EMA=NEUTRAL, Posizione BB=0.94\n- Previsione: UP",
    "Esempio 4:\n- Dati: RSI=63.5, Istogramma MACD=11.6, Trend EMA=BULLISH, Posizione BB=0.78\n- Previsione: UP",
    "Esempio 5:\n- Dati: RSI=59.8, Istogramma MACD=9.7, Trend EMA=BULLISH, Posizione BB=0.80\n- Previsione: UP",
    "Esempio 6:\n- Dati: RSI=56.4, Istogramma MACD=8.2, Trend EMA=BULLISH, Posizione BB=0.73\n- Previsione: UP",
    "Esempio 7:\n- Dati: RSI=54.7, Istogramma MACD=7.1, Trend EMA=BULLISH, Posizione BB=0.75\n- Previsione: UP"
  ],
  DOWN: [
    "Esempio 8:\n- Dati: RSI=28.5, Istogramma MACD=-16.4, Trend EMA=BEARISH, Posizione BB=0.12\n- Previsione: DOWN",
    "Esempio 9:\n- Dati: RSI=33.2, Istogramma MACD=-14.6, Trend EMA=BEARISH, Posizione BB=0.18\n- Previsione: DOWN",
    "Esempio 10:\n- Dati: RSI=31.8, Istogramma MACD=-12.8, Trend EMA=BEARISH, Posizione BB=0.15\n- Previsione: DOWN",
    "Esempio 11:\n- Dati: RSI=38.4, Istogramma MACD=-9.7, Trend EMA=BEARISH, Posizione BB=0.22\n- Previsione: DOWN",
    "Esempio 12:\n- Dati: RSI=41.2, Istogramma MACD=-8.3, Trend EMA=BEARISH, Posizione BB=0.20\n- Previsione: DOWN",
    "Esempio 13:\n- Dati: RSI=46.3, Istogramma MACD=-2.9, Trend EMA=NEUTRAL, Posizione BB=0.32\n- Previsione: DOWN",
    "Esempio 14:\n- Dati: RSI=48.2, Istogramma MACD=-2.5, Trend EMA=BEARISH, Posizione BB=0.35\n- Previsione: DOWN"
  ],
  LATERAL: [
    "Esempio 15:\n- Dati: RSI=49.3, Istogramma MACD=-0.9, Trend EMA=NEUTRAL, Posizione BB=0.48\n- Previsione: LATERAL",
    "Esempio 16:\n- Dati: RSI=50.5, Istogramma MACD=0.3, Trend EMA=NEUTRAL, Posizione BB=0.52\n- Previsione: LATERAL",
    "Esempio 17:\n- Dati: RSI=48.7, Istogramma MACD=-1.2, Trend EMA=NEUTRAL, Posizione BB=0.45\n- Previsione: LATERAL",
    "Esempio 18:\n- Dati: RSI=49.3, Istogramma MACD=-2.6, Trend EMA=BEARISH, Posizione BB=0.24\n- Previsione: LATERAL",
    "Esempio 19:\n- Dati: RSI=51.4, Istogramma MACD=1.8, Trend EMA=BULLISH, Posizione BB=0.56\n- Previsione: LATERAL"
  ]
};

app.post('/predict', async (req, res) => {
  const { rsi, macdHist, emaTrend, bbPosition, price, modelType } = req.body;

  if (rsi === undefined || macdHist === undefined || emaTrend === undefined || bbPosition === undefined || price === undefined) {
    return res.status(400).json({ error: 'Dati degli indicatori mancanti o incompleti.' });
  }
  
  // Selezione del modello da utilizzare
  let selectedModel;
  let modelName;
  
  switch(modelType) {
    case 'grok4':
      if (grokModel) {
        selectedModel = grokModel;
        modelName = 'grok4';
      } else {
        console.warn('Grok 4 richiesto ma non configurato, uso modello predefinito');
        selectedModel = model;
        modelName = 'deepseek';
      }
      break;
    case 'gemini':
      if (geminiModelWrapper) {
        selectedModel = geminiModelWrapper;
        modelName = 'gemini';
      } else {
        console.warn('Gemini richiesto ma non configurato, uso modello predefinito');
        selectedModel = model;
        modelName = 'deepseek';
      }
      break;
    case 'deepseek':
    default:
      selectedModel = model;
      modelName = 'deepseek';
  }

  // Prepara i dati attuali per il prompt
  const datiAttuali = {
    price,
    rsi,
    macdHist,
    emaTrend,
    bbPosition
  };
  
  // Leggi le predizioni passate per costruire esempi dinamici
  let prompt;
  try {
    // Leggi predizioni passate
    const predizioni = await readPredictions();
    
    console.log(`Utilizzando ${predizioni.length} predizioni passate per costruire esempi dinamici`);
    
    // Ottieni predizioni verificate, eventualmente filtrando per modello
    const predizioniVerificate = await ottieniPredizioniVerificate(predizioni, TIMEFRAME_ESEMPI, modelType);
    
    // Visualizza quante predizioni verificate abbiamo per ogni categoria
    console.log(`Predizioni verificate trovate - UP: ${predizioniVerificate.UP.length}, DOWN: ${predizioniVerificate.DOWN.length}, LATERAL: ${predizioniVerificate.LATERAL.length}`);
    
    // Seleziona esempi equilibrati
    const esempiSelezionati = selezionaEsempiEquilibrati(predizioniVerificate, esempiFissi);
    
    // Costruisci il prompt dinamico
    prompt = costruisciPromptDinamico(datiAttuali, esempiSelezionati, esempiFissi);
    
    console.log('Prompt dinamico costruito con successo');
  } catch (error) {
    console.error('Errore durante la costruzione del prompt dinamico:', error);
    console.log('Utilizzo esempi statici come fallback');
    
    // Fallback agli esempi statici predefiniti
    prompt = costruisciPromptDinamico(datiAttuali, {UP: [], DOWN: [], LATERAL: []}, esempiFissi);
  }

  if (!model) {
    console.error('Tentativo di predizione senza chiave API valida.');
    return res.status(500).json({ error: 'Il servizio di predizione non è configurato correttamente.' });
  }

  try {
    const result = await selectedModel.generateContent(prompt);
    const response = await result.response;
    const text = await response.text();
    let prediction = text.trim().toUpperCase();

    if (!['UP', 'DOWN', 'LATERAL'].includes(prediction)) {
      console.warn(`Risposta inattesa dal LLM: "${text.trim()}". La predizione deve essere UP, DOWN o LATERAL.`);
      return res.status(400).json({ 
        error: 'Risposta non valida dal modello', 
        receivedResponse: text.trim(),
        validOptions: ['UP', 'DOWN', 'LATERAL']
      });
    }

    const allPredictions = await readPredictions();

    const newPrediction = {
      id: Date.now(),
      timestamp: new Date().getTime(),
      priceAtPrediction: price,
      prediction: prediction,
      modelUsed: modelName,  
      indicators: {
        rsi,
        macdHist,
        emaTrend,
        bbPosition
      },
      evaluation: {
        '5m': { status: 'pending' },
        '10m': { status: 'pending' },
        '15m': { status: 'pending' },
        '1h': { status: 'pending' },
        '4h': { status: 'pending' },
        '8h': { status: 'pending' }, // Aggiunto timeframe 8h
        '24h': { status: 'pending' }
      }
    };

    allPredictions.push(newPrediction);
    await writePredictions(allPredictions);

    const metrics = calculateMetrics(allPredictions);

    res.json({ 
      prediction: newPrediction.prediction, 
      metrics,
      prompt, // Includo il prompt nella risposta
      modelUsed: modelName // Includo informazioni sul modello utilizzato
    });

  } catch (error) {
    console.error('Errore durante la chiamata all\'API DeepSeek:', error.message);
    
    // ANCHE IN CASO DI ERRORE, SALVIAMO UN TENTATIVO PER AVERE TRACCIA
    const allPredictions = await readPredictions();
    const errorPredictionEntry = {
      id: Date.now(),
      timestamp: new Date().getTime(),
      priceAtPrediction: price,
      prediction: 'ERROR',
      modelUsed: modelName, // Aggiungi il modello anche in caso di errore
      status: 'error',
      error: error.message,
      indicators: { rsi, macdHist, emaTrend, bbPosition },
      evaluation: {
        '5m': { status: 'pending' },
        '10m': { status: 'pending' },
        '15m': { status: 'pending' },
        '1h': { status: 'pending' },
        '4h': { status: 'pending' },
        '8h': { status: 'pending' },
        '24h': { status: 'pending' }
      }
    };
    allPredictions.push(errorPredictionEntry);
    await writePredictions(allPredictions);

    res.status(500).json({ error: 'Impossibile ottenere la predizione dal LLM' });
  }
});

app.post('/reset', async (req, res) => {
  try {
    // Svuota il file delle predizioni
    await writePredictions([]);
    console.log('Le metriche e le predizioni sono state resettate.');
    
    // Restituisce le metriche azzerate
    const initialMetrics = calculateMetrics([]);
    res.status(200).json({ 
      message: 'Metriche resettate con successo.',
      metrics: initialMetrics 
    });
  } catch (error) {
    console.error('Errore durante il reset delle metriche:', error);
    res.status(500).json({ error: 'Impossibile resettare le metriche.' });
  }
});

app.get('/history', async (req, res) => {
  try {
    const allPredictions = await readPredictions();
    res.status(200).json(allPredictions);
  } catch (error) {
    console.error('Errore durante il recupero dello storico:', error);
    res.status(500).json({ error: 'Impossibile recuperare lo storico delle predizioni.' });
  }
});

app.listen(port, () => {
  console.log(`Server backend in ascolto su http://localhost:${port}`);
  if (!model) {
    console.warn('ATTENZIONE: Il server è in esecuzione, ma il modello AI non è inizializzato. Le chiamate a /predict falliranno.');
  }
});
