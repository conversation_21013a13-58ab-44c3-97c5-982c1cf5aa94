# BTC Scalping App - Documentazione Completa

## Descrizione del Progetto

L'applicazione BTC Scalping è un sistema di trading algoritmico che utilizza modelli di intelligenza artificiale per predire i trend del prezzo di Bitcoin basandosi su indicatori tecnici. L'applicazione è composta da un frontend React e un backend Node.js che comunica con diversi modelli AI per generare predizioni accurate.

### Caratteristiche Principali

- **Predizioni di Trend**: Analizza indicatori tecnici (RSI, MACD, EMA, Bollinger Bands) per predire se il prezzo di Bitcoin andrà UP (su), DOWN (giù) o rimarrà LATERAL (laterale)
- **Sistema Multi-Modello**: Supporta diversi modelli AI (DeepSeek, Grok 4, Gemini) per le predizioni
- **Prompt Dinamici**: Utilizza esempi verificati di predizioni passate per migliorare l'accuratezza delle nuove predizioni
- **Valutazione Automatica**: Valuta l'accuratezza delle predizioni su diversi timeframe (5m, 10m, 15m, 1h, 4h, 8h, 24h)
- **Metriche di Performance**: Calcola e visualizza metriche di accuratezza per ogni modello e timeframe

## Architettura del Sistema

### Struttura delle Directory

```
server/
├── config/           # Configurazioni e costanti
├── controller/       # Logica di business
├── modelli/          # Definizione dei modelli dati
├── route/            # Definizione delle route API
├── servizi/          # Servizi per funzionalità specifiche
├── utils/            # Utilità generiche
├── data/             # Dati persistenti (predizioni)
├── .env              # Variabili d'ambiente
└── server.js         # Punto di ingresso dell'applicazione
```

### Componenti Principali

1. **Server Express**: Gestisce le richieste HTTP e coordina i vari componenti
2. **Controller Predizioni**: Genera predizioni utilizzando i modelli AI
3. **Controller Valutazioni**: Valuta l'accuratezza delle predizioni passate
4. **Servizio Modelli AI**: Gestisce la comunicazione con i diversi modelli AI
5. **Servizio Prompt Builder**: Costruisce prompt dinamici utilizzando esempi verificati
6. **Servizio Archiviazione**: Gestisce la persistenza delle predizioni

## Parametri e Variabili

### Variabili d'Ambiente

| Nome | Descrizione | Obbligatorio |
|------|-------------|-------------|
| `deepseek_api_key` | Chiave API per DeepSeek | Sì |
| `OPENROUTER_API_KEY` | Chiave API per OpenRouter (Grok 4) | No |
| `GOOGLE_API_KEY` | Chiave API per Google AI (Gemini) | No |

### Costanti di Configurazione

| Nome | Valore | Descrizione |
|------|--------|------------|
| `PORTA_SERVER` | 3004 | Porta su cui il server ascolta |
| `SOGLIA_PROFITTO` | 0.005 | Soglia per determinare un movimento di prezzo significativo (0.5%) |
| `ESEMPI_PER_CATEGORIA` | 5 | Numero di esempi da utilizzare per ogni categoria di trend |
| `MIN_ESEMPI_PER_CATEGORIA` | 3 | Numero minimo di esempi richiesti per usare esempi dinamici |
| `TIMEFRAME_ESEMPI` | '4h' | Timeframe da utilizzare per selezionare esempi verificati |
| `MAX_ETÀ_ESEMPI` | 30 giorni | Età massima degli esempi da considerare |
| `TIMEFRAME_VALUTAZIONE` | Vari | Timeframe per valutare le predizioni (5m, 10m, 15m, 1h, 4h, 8h, 24h) |

### Tipi di Dati

#### Oggetto Predizione

```javascript
{
  id: Number,                  // ID univoco della predizione
  timestamp: Number,           // Timestamp della predizione
  priceAtPrediction: Number,   // Prezzo di BTC al momento della predizione
  prediction: String,          // Predizione: 'UP', 'DOWN', 'LATERAL' o 'ERROR'
  modelUsed: String,           // Modello utilizzato: 'deepseek', 'grok4', 'gemini'
  indicators: {                // Indicatori tecnici utilizzati
    rsi: Number,               // Relative Strength Index
    macdHist: Number,          // MACD Histogram
    emaTrend: String,          // Trend EMA: 'BULLISH', 'BEARISH', 'NEUTRAL'
    bbPosition: Number         // Posizione nelle Bollinger Bands (-1 a 1)
  },
  evaluation: {                // Valutazione della predizione per ogni timeframe
    '5m': { status: String },  // Status: 'pending', 'correct', 'incorrect'
    '10m': { status: String },
    '15m': { status: String },
    '1h': { status: String },
    '4h': { status: String },
    '8h': { status: String },
    '24h': { status: String }
  }
}
```

## API e Endpoint

### POST /predict

Genera una nuova predizione basata su indicatori tecnici.

**Richiesta:**
```json
{
  "rsi": 65.4,
  "macdHist": 10.5,
  "emaTrend": "BULLISH",
  "bbPosition": 0.75,
  "price": 115000,
  "modelType": "deepseek"
}
```

**Risposta:**
```json
{
  "prediction": "UP",
  "metrics": { ... },
  "prompt": "...",
  "modelUsed": "deepseek"
}
```

### POST /evaluate

Valuta le predizioni pendenti confrontando il prezzo attuale con quello al momento della predizione.

**Richiesta:**
```json
{
  "timeframe": "1h"
}
```

**Risposta:**
```json
{
  "message": "Valutate 3 predizioni",
  "updatedCount": 3,
  "timeframe": "1h"
}
```

### GET /metrics

Ottiene le metriche di accuratezza delle predizioni.

**Risposta:**
```json
{
  "totale": 100,
  "corrette": 65,
  "incorrette": 35,
  "pending": 0,
  "accuratezza": "65.00",
  "perTimeframe": { ... },
  "perTrend": { ... }
}
```

### GET /predictions

Ottiene tutte le predizioni salvate.

**Risposta:**
```json
[
  {
    "id": 1689504000000,
    "timestamp": 1689504000000,
    "priceAtPrediction": 115000,
    "prediction": "UP",
    "modelUsed": "deepseek",
    "indicators": { ... },
    "evaluation": { ... }
  },
  ...
]
```

## Sistema di Prompt Dinamici

Il sistema di prompt dinamici è una caratteristica chiave dell'applicazione che migliora l'accuratezza delle predizioni utilizzando esempi verificati di predizioni passate invece di esempi statici hardcoded.

### Funzionamento

1. **Raccolta di Esempi Verificati**:
   - Estrae predizioni passate che sono state valutate come corrette
   - Filtra per recenza (max 30 giorni) e opzionalmente per modello AI

2. **Selezione Bilanciata**:
   - Seleziona un numero equilibrato di esempi per ogni categoria (UP, DOWN, LATERAL)
   - Richiede almeno 3 esempi per categoria per utilizzare esempi dinamici

3. **Fallback Intelligente**:
   - Se non ci sono abbastanza esempi verificati, utilizza esempi statici predefiniti
   - Garantisce sempre un prompt ben formato con esempi per ogni categoria

4. **Costruzione del Prompt**:
   - Formatta gli esempi in modo coerente
   - Combina gli esempi con i dati attuali per creare il prompt completo

### Implementazione

Il sistema è implementato attraverso le seguenti funzioni principali:

- `ottieniPredizioniVerificate`: Estrae predizioni verificate dal database
- `selezionaEsempiEquilibrati`: Seleziona esempi bilanciati per categoria
- `formattaEsempio`: Formatta un esempio per il prompt
- `costruisciPromptDinamico`: Costruisce il prompt completo

## Modelli AI Supportati

L'applicazione supporta tre diversi modelli AI per le predizioni:

1. **DeepSeek** (default):
   - Modello: `deepseek-chat`
   - Configurazione: OpenAI API compatibile

2. **Grok 4** (via OpenRouter):
   - Modello: `openrouter/xai/grok-4`
   - Configurazione: OpenRouter API

3. **Gemini** (via Google AI):
   - Modello: `gemini-1.5-pro`
   - Configurazione: Google Generative AI SDK

## Istruzioni per l'Installazione

1. **Clona il repository**:
   ```bash
   git clone <repository-url>
   cd btc-scalping-app
   ```

2. **Installa le dipendenze**:
   ```bash
   npm install
   ```

3. **Configura le variabili d'ambiente**:
   Crea un file `.env` nella cartella `server/` con le tue chiavi API:
   ```
   deepseek_api_key=your_deepseek_api_key_here
   OPENROUTER_API_KEY=your_openrouter_api_key_here
   GOOGLE_API_KEY=your_google_api_key_here
   ```

4. **Avvia l'applicazione**:
   ```bash
   bash avvia-app.sh
   ```

5. **Chiudi l'applicazione**:
   ```bash
   bash chiudi-app.sh
   ```

## Best Practice Implementate

- **Modularità**: Codice organizzato in moduli con responsabilità ben definite
- **Gestione degli Errori**: Sistema robusto di gestione degli errori con fallback
- **Logging**: Sistema di logging dettagliato per debugging e monitoraggio
- **Documentazione**: Documentazione completa con JSDoc per tutte le funzioni
- **Nomenclatura Coerente**: Nomi di variabili e funzioni in italiano per coerenza
- **Configurazione Centralizzata**: Tutte le costanti e configurazioni in un unico file
- **Validazione Input**: Validazione dei dati di input per prevenire errori
- **Backup dei Dati**: Sistema di backup per prevenire la perdita di dati

## Implementazioni Recenti

- **Sistema di Prompt Dinamici**: Miglioramento dell'accuratezza delle predizioni
- **Supporto Multi-Modello**: Aggiunta del supporto per Grok 4 e Gemini
- **Timeframe 8h**: Aggiunta del timeframe di 8 ore per la valutazione delle predizioni
- **Refactoring del Codebase**: Riorganizzazione del codice secondo best practice
- **Logging Migliorato**: Sistema di logging più dettagliato e colorato
