// Script per aggiornare la struttura delle predizioni
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// Ottieni la directory corrente in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PREDICTIONS_FILE = path.join(__dirname, 'predictions.json');

async function updatePredictions() {
  try {
    // Leggi il file delle predizioni
    const data = await fs.readFile(PREDICTIONS_FILE, 'utf8');
    const predictions = JSON.parse(data);
    
    console.log(`Predizioni totali da aggiornare: ${predictions.length}`);
    
    // Aggiorna ogni predizione
    const updatedPredictions = predictions.map(p => {
      // Se la predizione non ha una struttura evaluation, creala
      if (!p.evaluation) {
        p.evaluation = {};
      }
      
      // Aggiungi i nuovi timeframe se non esistono già
      const newTimeframes = ['1h', '4h', '24h'];
      newTimeframes.forEach(tf => {
        if (!p.evaluation[tf]) {
          p.evaluation[tf] = { status: 'pending' };
        }
      });
      
      // Aggiungi il campo modelUsed se non esiste
      if (!p.modelUsed) {
        p.modelUsed = 'deepseek'; // Valore predefinito per le predizioni esistenti
      }
      
      return p;
    });
    
    console.log('Predizioni aggiornate con i nuovi timeframe');
    
    // Salva le predizioni aggiornate
    await fs.writeFile(PREDICTIONS_FILE, JSON.stringify(updatedPredictions, null, 2));
    console.log('File delle predizioni aggiornato con successo!');
    
  } catch (error) {
    console.error('Errore durante l\'aggiornamento delle predizioni:', error);
  }
}

// Esegui la funzione
updatePredictions();
