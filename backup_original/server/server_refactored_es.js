/**
 * Server principale dell'applicazione BTC Scalping
 * Implementa un'API RESTful per la predizione e valutazione dei trend di Bitcoin
 */

import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import { PORTA_SERVER } from './config/index.js';
import routes from './route/index.js';
import { inizializzaClientiAI } from './servizi/modelliAI.js';

// Configurazione delle variabili di percorso
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Inizializzazione dell'app Express
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../dist')));

// Inizializzazione dei client AI
inizializzaClientiAI();

// Registrazione delle route
app.use('/', routes);

// Gestione degli errori
app.use((err, req, res, next) => {
  console.error('Errore non gestito:', err);
  res.status(500).json({
    error: 'Errore interno del server',
    message: err.message
  });
});

// Avvio del server
app.listen(PORTA_SERVER, () => {
  console.log(`Server backend in ascolto su http://localhost:${PORTA_SERVER}`);
});

// Gestione della terminazione del processo
process.on('SIGINT', () => {
  console.log('Server in chiusura...');
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  console.error('Eccezione non gestita:', err);
  process.exit(1);
});

export default app;
