/**
 * Controller per la gestione delle valutazioni delle predizioni
 * Gestisce la logica per valutare l'accuratezza delle predizioni passate
 */

const { leggiPredizioni, scriviPredizioni } = require('../servizi/archiviazione');
const { SOGLIA_PROFITTO, TIMEFRAME_VALUTAZIONE } = require('../config');

/**
 * Valuta le predizioni pendenti confrontando il prezzo attuale con quello al momento della predizione
 * @param {string} [timeframe='1h'] - Timeframe da utilizzare per la valutazione
 * @returns {Promise<Object>} Risultato della valutazione
 */
async function valutaPredizioni(timeframe = '1h') {
  try {
    // Leggi tutte le predizioni
    let predizioni = await leggiPredizioni();
    
    // Filtra le predizioni da valutare (quelle con status 'pending')
    const predizioniDaValutare = predizioni.filter(p => 
      p.evaluation && Object.values(p.evaluation).some(e => e.status === 'pending')
    );
    
    // Se non ci sono predizioni da valutare, restituisci un risultato vuoto
    if (predizioniDaValutare.length === 0) {
      return { 
        message: 'Nessuna predizione da valutare', 
        updatedCount: 0 
      };
    }
    
    // Ottieni il prezzo attuale di BTC
    const prezzoAttuale = await ottieniPrezzoAttuale();
    const now = Date.now();
    let contatorePredAggiornate = 0;
    
    // Valuta ogni predizione
    for (const predizione of predizioniDaValutare) {
      const prezzoAlMomentoPredizione = predizione.priceAtPrediction;
      let trendAttuale;
      
      // Determina il trend attuale in base alla differenza di prezzo
      if (prezzoAttuale > prezzoAlMomentoPredizione * (1 + SOGLIA_PROFITTO)) {
        trendAttuale = 'UP';
      } else if (prezzoAttuale < prezzoAlMomentoPredizione * (1 - SOGLIA_PROFITTO)) {
        trendAttuale = 'DOWN';
      } else {
        trendAttuale = 'LATERAL';
      }
      
      // Aggiorna lo stato di valutazione per ogni timeframe
      for (const tf in predizione.evaluation) {
        const millisecondiTimeframe = TIMEFRAME_VALUTAZIONE[tf];
        
        // Verifica se è passato abbastanza tempo per valutare questo timeframe
        if (predizione.evaluation[tf].status === 'pending' && now - predizione.timestamp > millisecondiTimeframe) {
          predizione.evaluation[tf].status = predizione.prediction === trendAttuale ? 'correct' : 'incorrect';
          predizione.evaluation[tf].actualTrend = trendAttuale;
          predizione.evaluation[tf].evaluatedAt = now;
          predizione.evaluation[tf].priceAtEvaluation = prezzoAttuale;
          contatorePredAggiornate++;
        }
      }
    }
    
    // Se sono state aggiornate delle predizioni, salva le modifiche
    if (contatorePredAggiornate > 0) {
      await scriviPredizioni(predizioni);
    }
    
    return {
      message: `Valutate ${contatorePredAggiornate} predizioni`,
      updatedCount: contatorePredAggiornate,
      timeframe
    };
  } catch (errore) {
    console.error('Errore durante la valutazione delle predizioni:', errore);
    throw errore;
  }
}

/**
 * Ottiene il prezzo attuale di BTC da Binance
 * @returns {Promise<number>} Prezzo attuale di BTC
 */
async function ottieniPrezzoAttuale() {
  try {
    const risposta = await fetch('https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT');
    
    if (!risposta.ok) {
      throw new Error(`Errore API Binance: ${risposta.status} ${risposta.statusText}`);
    }
    
    const dati = await risposta.json();
    return parseFloat(dati.price);
  } catch (errore) {
    console.error('Errore durante il recupero del prezzo attuale:', errore);
    throw errore;
  }
}

module.exports = {
  valutaPredizioni,
  ottieniPrezzoAttuale
};
