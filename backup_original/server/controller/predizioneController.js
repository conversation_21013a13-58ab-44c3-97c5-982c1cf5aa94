/**
 * Controller per la gestione delle predizioni
 * Gestisce la logica per generare predizioni utilizzando i modelli AI
 */

const { eseguiPredizione, TIPI_MODELLO } = require('../servizi/modelliAI');
const { leggiPredizioni, scriviPredizioni } = require('../servizi/archiviazione');
const { 
  ottieniPredizioniVerificate, 
  selezionaEsempiEquilibrati, 
  costruisciPromptDinamico,
  esempiFissi 
} = require('../servizi/promptBuilder');
const { calcolaMetriche } = require('../utils/metriche');

/**
 * Genera una nuova predizione utilizzando il modello AI specificato
 * @param {Object} datiIndicatori - Dati degli indicatori tecnici
 * @param {string} tipoModello - Tipo di modello AI da utilizzare
 * @returns {Promise<Object>} Risultato della predizione
 */
async function generaPredizione(datiIndicatori, tipoModello) {
  try {
    const { rsi, macdHist, emaTrend, bbPosition, price } = datiIndicatori;
    
    // Validazione dei dati di input
    if (!rsi || !macdHist || !emaTrend || !bbPosition || !price) {
      throw new Error('Dati degli indicatori incompleti');
    }
    
    // Tipo di modello predefinito se non specificato
    const modelType = tipoModello || TIPI_MODELLO.DEEPSEEK;
    
    // Leggi le predizioni esistenti
    const predizioni = await leggiPredizioni();
    
    // Ottieni predizioni verificate
    const predizioniVerificate = ottieniPredizioniVerificate(predizioni, modelType);
    console.log(`Trovate ${Object.values(predizioniVerificate).flat().length} predizioni verificate`);
    console.log(`- UP: ${predizioniVerificate.UP.length}`);
    console.log(`- DOWN: ${predizioniVerificate.DOWN.length}`);
    console.log(`- LATERAL: ${predizioniVerificate.LATERAL.length}`);
    
    // Seleziona esempi equilibrati
    const esempiSelezionati = selezionaEsempiEquilibrati(predizioniVerificate, esempiFissi);
    
    // Costruisci il prompt dinamico
    const datiAttuali = { rsi, macdHist, emaTrend, bbPosition };
    const prompt = costruisciPromptDinamico(datiAttuali, esempiSelezionati, esempiFissi);
    
    // Esegui la predizione con il modello AI
    const risposta = await eseguiPredizione(modelType, prompt);
    
    // Estrai la predizione dalla risposta
    const predizione = estraiPredizione(risposta);
    
    // Crea il nuovo oggetto predizione
    const nuovaPredizione = {
      id: Date.now(),
      timestamp: new Date().getTime(),
      priceAtPrediction: price,
      prediction: predizione,
      modelUsed: modelType,
      indicators: {
        rsi,
        macdHist,
        emaTrend,
        bbPosition
      },
      evaluation: {
        '5m': { status: 'pending' },
        '10m': { status: 'pending' },
        '15m': { status: 'pending' },
        '1h': { status: 'pending' },
        '4h': { status: 'pending' },
        '8h': { status: 'pending' },
        '24h': { status: 'pending' }
      }
    };
    
    // Salva la nuova predizione
    predizioni.push(nuovaPredizione);
    await scriviPredizioni(predizioni);
    
    // Calcola le metriche
    const metriche = calcolaMetriche(predizioni);
    
    return {
      prediction: nuovaPredizione.prediction,
      metrics: metriche,
      prompt,
      modelUsed: modelType
    };
  } catch (errore) {
    console.error('Errore durante la generazione della predizione:', errore);
    
    // Anche in caso di errore, salviamo un tentativo per avere traccia
    try {
      const { price, rsi, macdHist, emaTrend, bbPosition } = datiIndicatori;
      const predizioni = await leggiPredizioni();
      
      const errorePredizione = {
        id: Date.now(),
        timestamp: new Date().getTime(),
        priceAtPrediction: price,
        prediction: 'ERROR',
        modelUsed: tipoModello,
        indicators: { rsi, macdHist, emaTrend, bbPosition },
        evaluation: {
          '5m': { status: 'pending' },
          '10m': { status: 'pending' },
          '15m': { status: 'pending' },
          '1h': { status: 'pending' },
          '4h': { status: 'pending' },
          '8h': { status: 'pending' },
          '24h': { status: 'pending' }
        },
        error: errore.message
      };
      
      predizioni.push(errorePredizione);
      await scriviPredizioni(predizioni);
    } catch (erroreSecondario) {
      console.error('Errore durante il salvataggio dell\'errore di predizione:', erroreSecondario);
    }
    
    throw errore;
  }
}

/**
 * Estrae la predizione dalla risposta del modello AI
 * @param {string} risposta - Risposta del modello AI
 * @returns {string} Predizione estratta (UP, DOWN o LATERAL)
 */
function estraiPredizione(risposta) {
  const testoRisposta = risposta.trim().toUpperCase();
  
  if (testoRisposta.includes('UP')) {
    return 'UP';
  } else if (testoRisposta.includes('DOWN')) {
    return 'DOWN';
  } else if (testoRisposta.includes('LATERAL')) {
    return 'LATERAL';
  }
  
  // Se non è possibile estrarre una predizione valida, lancia un errore
  throw new Error(`Impossibile estrarre una predizione valida dalla risposta: ${risposta}`);
}

module.exports = {
  generaPredizione
};
