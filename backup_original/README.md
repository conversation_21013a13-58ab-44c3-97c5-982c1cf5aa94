# BTC Scalping App con AI (Google Gemini)

Questo progetto è un'applicazione web progettata per lo scalping su Bitcoin (BTC/USDT). Integra dati di mercato in tempo reale, calcola indicatori tecnici e utilizza un modello di linguaggio di grandi dimensioni (LLM), specificamente Google Gemini, per prevedere i movimenti di prezzo a breve termine.

## Architettura

L'applicazione è composta da due componenti principali che vengono eseguiti contemporaneamente:

1.  **Frontend**: Un'applicazione single-page (SPA) costruita con **React** e **TypeScript**, utilizzando **Vite** come build tool. Si occupa di:
    *   Visualizzare i dati di prezzo in un grafico (con **Recharts**).
    *   Mostrare gli indicatori tecnici e le previsioni dell'AI.
    *   Gestire l'interfaccia utente, stilizzata con **Tailwind CSS**.

2.  **Backend**: Un server leggero costruito con **Node.js** e **Express**. Le sue responsabilità sono:
    *   Esporre un endpoint API `/predict`.
    *   Gestire in modo sicuro la chiave API di Google.
    *   Comunicare con l'API di **Google Gemini** per generare le previsioni di trend.

## Funzionalità Principali

- **Dati in Tempo Reale**: Si connette al WebSocket di Binance (`wss://stream.binance.com:9443/ws/btcusdt@trade`) per ricevere ogni singolo trade in tempo reale.

- **Aggregazione in Candele da 1 Minuto**: Per evitare il rumore dei dati tick-by-tick, l'applicazione aggrega i trade in candele OHLC (Open, High, Low, Close) da 1 minuto. Gli indicatori vengono calcolati solo sul prezzo di chiusura di ogni candela.

- **Pre-caricamento Dati Storici**: All'avvio, l'app effettua una chiamata API a Binance per scaricare le ultime 30 candele da 1 minuto, garantendo che gli indicatori siano disponibili e affidabili fin da subito.

- **Calcolo di Indicatori Tecnici**: Calcola i seguenti indicatori basandosi sullo storico dei prezzi di chiusura delle candele:
    - **RSI** (Relative Strength Index)
    - **MACD** (Moving Average Convergence Divergence)
    - **Medie Mobili Esponenziali** (EMA)
    - **Bande di Bollinger** (Bollinger Bands)
    - **Rapporto sul Volume**

- **Previsioni AI con Google Gemini**: Quando vengono calcolati nuovi indicatori, questi vengono formattati in un prompt dettagliato e inviati al backend. Il server interroga il modello `gemini-1.5-pro-latest` per ottenere una previsione di trend (`UP`, `DOWN`, `LATERAL`).

- **Monitoraggio delle Performance**: L'applicazione traccia l'accuratezza delle previsioni confrontando il trend previsto con il movimento di prezzo reale dopo 90 secondi.

## Guida all'Installazione e Avvio

### Prerequisiti

- [Node.js](https://nodejs.org/) (versione 18.x o superiore)
- `npm` (incluso con Node.js)

### Procedura

1.  **Clonare il Repository** (se applicabile):
    ```bash
    git clone <URL_DEL_REPOSITORY>
    cd btc-scalping-app
    ```

2.  **Installare le Dipendenze**:
    Eseguire il seguente comando nella cartella principale del progetto. Installerà tutte le dipendenze sia per il frontend che per il backend.
    ```bash
    npm install
    ```

3.  **Configurare le Variabili d'Ambiente**:
    Questo è il passaggio più importante per far funzionare l'AI.
    - Crea una copia del file `.env.example` e rinominala in `.env`.
    - Apri il nuovo file `.env` e inserisci la tua chiave API di Google:
      ```
      # File per le variabili d'ambiente. Non committare su Git.
      GOOGLE_API_KEY="LA_TUA_CHIAVE_API_VALIDA_QUI"
      ```
    **Nota**: La chiave API deve essere valida e abilitata per l'uso con "Google AI Platform".

4.  **Avviare l'Applicazione**:
    Esegui il seguente comando per avviare entrambi i server (frontend e backend) contemporaneamente:
    ```bash
    npm run dev
    ```

5.  **Accedere all'App**:
    Apri il tuo browser e naviga all'indirizzo [http://localhost:5173](http://localhost:5173).

## Script di Avvio e Gestione

L'applicazione include diversi script per semplificare l'avvio, l'arresto e la gestione dei server:

### Comandi NPM

Puoi utilizzare i seguenti comandi npm per gestire l'applicazione:

```bash
# Avvia entrambi i server (frontend e backend) in modalità sviluppo
npm run dev

# Avvia entrambi i server con accesso da altri dispositivi sulla rete
npm run start

# Ferma tutti i processi node in esecuzione
npm run stop

# Riavvia l'applicazione (ferma e poi avvia)
npm run restart
```

### Script Bash Semplificato

È disponibile anche uno script bash `avvia.sh` che semplifica ulteriormente la gestione dell'applicazione con comandi in italiano:

```bash
# Rendi lo script eseguibile (solo la prima volta)
chmod +x avvia.sh

# Avvia entrambi i server
./avvia.sh avvia

# Ferma tutti i processi node
./avvia.sh ferma

# Riavvia entrambi i server
./avvia.sh riavvia

# Mostra lo stato dei server
./avvia.sh stato

# Mostra il messaggio di aiuto
./avvia.sh aiuto
```

Questo script include output colorato per una migliore leggibilità e controllo dello stato dei server sulle porte 3004 (backend) e 5173 (frontend).

## Struttura del Codice

- `public/`: File statici.
- `src/`: Contiene tutto il codice sorgente del frontend React.
  - `BTCScalpingApp.tsx`: Il componente principale che contiene tutta la logica dell'applicazione (gestione stato, WebSocket, calcoli, UI).
- `server/`: Contiene il codice del backend.
  - `server.js`: Il server Express che gestisce le chiamate all'API di Gemini.
- `.env`: File **locale e non tracciato** per le chiavi API segrete.
- `.env.example`: File di esempio per le variabili d'ambiente.
- `package.json`: Definisce le dipendenze del progetto e gli script (`dev`, `build`, etc.).
- `README.md`: Questa documentazione.

## Note per Sviluppi Futuri

- **Risoluzione Problema Chiave API**: Se le previsioni continuano a non funzionare, verificare che la `GOOGLE_API_KEY` sia corretta, non scaduta e che l'account Google associato abbia i permessi necessari e un metodo di fatturazione attivo, se richiesto da Google.
- **Strategie Multiple**: Si potrebbe rendere la strategia di trading (soglie, indicatori usati) configurabile dall'utente.
- **Error Handling**: Migliorare la gestione degli errori, specialmente per le connessioni di rete (WebSocket e API call).
