#!/bin/bash

# Script per avviare sia il frontend che il backend dell'applicazione BTC Scalping

# Colori per i messaggi
VERDE='\033[0;32m'
GIALLO='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${VERDE}===============================================${NC}"
echo -e "${VERDE}   AVVIO BTC SCALPING APP   ${NC}"
echo -e "${VERDE}===============================================${NC}"
echo

# Verifica che Node.js sia installato
if ! command -v node &> /dev/null; then
    echo -e "${GIALLO}Node.js non trovato. Assicurati di averlo installato.${NC}"
    exit 1
fi

# Directory principale del progetto
DIR_PROGETTO="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$DIR_PROGETTO"

# Verifica che tutti i pacchetti siano installati
echo -e "${VERDE}Verifica delle dipendenze...${NC}"
npm install

echo
echo -e "${VERDE}Avvio dell'applicazione...${NC}"
echo -e "${GIALLO}Premi Ctrl+C per terminare l'applicazione${NC}"
echo

# Avvia l'applicazione usando lo script 'dev' definito nel package.json
# Questo script usa concurrently per avviare sia il frontend che il backend
npm run dev

# Questo script non raggiungerà mai questa parte a meno che npm run dev non termini
echo -e "${VERDE}Applicazione terminata.${NC}"
