<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>BTC Scalping AI</title>
    <script>
      // Mock per l'API di Claude
      window.claude = {
        complete: (prompt) => {
          return new Promise(resolve => {
            setTimeout(() => {
              const responses = ['UP', 'DOWN', 'LATERAL'];
              const randomResponse = responses[Math.floor(Math.random() * responses.length)];
              console.log('Claude API Mocked. Prompt:', prompt);
              console.log('Returning mocked response:', randomResponse);
              resolve(randomResponse);
            }, 500 + Math.random() * 500); // Simula latenza di rete
          });
        }
      };
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
